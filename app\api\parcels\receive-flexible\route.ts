import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

interface ReceiveParcelEntry {
  lr_number: string;
  received_quantity: number;
}

interface ReceiveRequest {
  vehicle_id: number;
  vehicle_registration: string;
  receiving_branch_id: number;
  parcels: ReceiveParcelEntry[];
  remarks?: string;
}

// POST /api/parcels/receive-flexible
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: ReceiveRequest = await request.json();

    // Validate required fields
    if (
      !body.vehicle_id || !body.receiving_branch_id || !body.parcels ||
      !Array.isArray(body.parcels)
    ) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: vehicle_id, receiving_branch_id, parcels",
        },
        { status: 400 },
      );
    }

    if (body.parcels.length === 0) {
      return NextResponse.json(
        { error: "At least one parcel must be provided" },
        { status: 400 },
      );
    }

    const results = [];

    // Process each parcel
    for (const entry of body.parcels) {
      try {
        // Validate parcel exists and can be received
        const { data: parcel, error: parcelError } = await supabase
          .from("parcels")
          .select(`
            parcel_id,
            lr_number,
            sender_name,
            recipient_name,
            number_of_items,
            current_status,
            delivery_branch_id,
            sender_branch_id,
            sender_branch:branches!sender_branch_id(name, code),
            delivery_branch:branches!delivery_branch_id(name, code)
          `)
          .eq("lr_number", entry.lr_number)
          .single();

        if (parcelError || !parcel) {
          results.push({
            lr_number: entry.lr_number,
            success: false,
            error: "Parcel not found",
          });
          continue;
        }

        // Check if parcel status allows receiving
        const allowedStatuses = ["Booked", "Loaded", "Received"];
        if (!allowedStatuses.includes(parcel.current_status)) {
          results.push({
            lr_number: entry.lr_number,
            success: false,
            error:
              `Cannot receive parcel with status: ${parcel.current_status}`,
          });
          continue;
        }

        // Validate received quantity
        if (entry.received_quantity <= 0) {
          results.push({
            lr_number: entry.lr_number,
            success: false,
            error: "Received quantity must be greater than 0",
          });
          continue;
        }

        // Get current received quantity for this parcel at this branch
        const { data: existingActions, error: actionsError } = await supabase
          .from("parcel_actions")
          .select("quantity_received")
          .eq("parcel_id", parcel.parcel_id)
          .eq("action_type", "Received")
          .eq("branch_id", body.receiving_branch_id);

        if (actionsError) {
          console.error("Error checking existing actions:", actionsError);
          results.push({
            lr_number: entry.lr_number,
            success: false,
            error: "Failed to check existing received quantities",
          });
          continue;
        }

        const currentlyReceived = existingActions?.reduce((sum, action) =>
          sum + (action.quantity_received || 0), 0) || 0;
        const totalAfterReceiving = currentlyReceived + entry.received_quantity;

        // Note: Quantity validation removed for receiving operators
        // Backend still tracks actual quantities but doesn't expose warnings to UI

        // Get receiving branch name for location
        const { data: receivingBranch } = await supabase
          .from("branches")
          .select("name")
          .eq("branch_id", body.receiving_branch_id)
          .single();

        // Get vehicle information for the action record
        const { data: vehicleData } = await supabase
          .from("vehicles")
          .select("vehicle_number")
          .eq("vehicle_id", body.vehicle_id)
          .single();

        // Create parcel action record
        const { error: actionError } = await supabase
          .from("parcel_actions")
          .insert({
            parcel_id: parcel.parcel_id,
            action_type: "Received",
            action_timestamp: new Date().toISOString(),
            branch_id: body.receiving_branch_id,
            location_name: receivingBranch?.name || null,
            vehicle_id: body.vehicle_id,
            vehicle_number: vehicleData?.vehicle_number || null,
            chart_number: null, // Flexible receiving doesn't use charts
            quantity: entry.received_quantity,
            quantity_received: entry.received_quantity,
            remarks: body.remarks || "Received via Receiving system",
            created_by: session.user.id,
          });

        if (actionError) {
          console.error("Error creating parcel action:", actionError);
          results.push({
            lr_number: entry.lr_number,
            success: false,
            error: "Failed to record receiving action",
          });
          continue;
        }

        // Determine new parcel status based on received quantities
        let newStatus = parcel.current_status;

        // Only update status when ALL items have been received
        if (totalAfterReceiving >= parcel.number_of_items) {
          // All items received - set to "Received" regardless of branch
          // "Delivered" status can only be set through manual delivery action
          newStatus = "Received";
        } else if (parcel.current_status === "Booked") {
          // If parcel was only booked and now received somewhere (partial), mark as "Loaded" (in transit)
          newStatus = "Loaded";
        }
        // For "Loaded" parcels with partial receives, status remains "Loaded"

        // Update parcel status if it changed
        if (newStatus !== parcel.current_status) {
          const { error: updateError } = await supabase
            .from("parcels")
            .update({ current_status: newStatus })
            .eq("parcel_id", parcel.parcel_id);

          if (updateError) {
            console.error("Error updating parcel status:", updateError);
            // Don't fail the operation, just log the error
          }
        }

        results.push({
          lr_number: entry.lr_number,
          success: true,
          received_quantity: entry.received_quantity,
          // Hide sensitive information from receiving operators
          parcel_details: {
            sender_name: parcel.sender_name,
            recipient_name: parcel.recipient_name,
            sender_branch: (parcel.sender_branch as any)?.name || "Unknown",
            delivery_branch: (parcel.delivery_branch as any)?.name || "Unknown",
          },
        });
      } catch (error: any) {
        console.error(`Error processing parcel ${entry.lr_number}:`, error);
        results.push({
          lr_number: entry.lr_number,
          success: false,
          error: "Internal error processing parcel",
        });
      }
    }

    return NextResponse.json({
      message: "Receiving operation completed",
      results,
      summary: {
        total_parcels: body.parcels.length,
        successful: results.filter((r) => r.success).length,
        failed: results.filter((r) => !r.success).length,
      },
    });
  } catch (error: any) {
    console.error("Error in POST /api/parcels/receive-flexible:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
