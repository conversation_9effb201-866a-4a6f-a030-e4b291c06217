"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Plus, 
  X, 
  MessageSquare, 
  Phone,
  Users,
  Send,
  Loader2
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface WhatsAppShareDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onShare: (phoneNumbers: string[], message: string) => Promise<void>
  reportType: string
  reportNumber: string
}

interface Contact {
  id: string
  name: string
  phone: string
}

export function WhatsAppShareDialog({
  open,
  onO<PERSON>Chang<PERSON>,
  onShare,
  reportType,
  reportNumber
}: WhatsAppShareDialogProps) {
  const { toast } = useToast()
  const [phoneNumbers, setPhoneNumbers] = useState<Contact[]>([])
  const [newContactName, setNewContactName] = useState("")
  const [newContactPhone, setNewContactPhone] = useState("")
  const [message, setMessage] = useState("")
  const [isSharing, setIsSharing] = useState(false)
  const [savedContacts, setSavedContacts] = useState<Contact[]>([])

  // Load saved contacts from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('whatsapp-contacts')
    if (saved) {
      try {
        setSavedContacts(JSON.parse(saved))
      } catch (error) {
        console.error('Error loading saved contacts:', error)
      }
    }
  }, [])

  // Set default message when dialog opens
  useEffect(() => {
    if (open) {
      setMessage(
        `📋 *${reportType}*\n` +
        `Report Number: ${reportNumber}\n` +
        `Date: ${new Date().toLocaleDateString()}\n\n` +
        `Please find the attached ${reportType.toLowerCase()} report.\n\n` +
        `Best regards,\n` +
        `KPN Parcel Service`
      )
    }
  }, [open, reportType, reportNumber])

  const addContact = () => {
    if (!newContactName.trim() || !newContactPhone.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter both name and phone number.",
        variant: "destructive",
      })
      return
    }

    // Validate phone number (basic validation)
    const phoneRegex = /^[+]?[\d\s\-()]{10,15}$/
    if (!phoneRegex.test(newContactPhone.trim())) {
      toast({
        title: "Invalid Phone Number",
        description: "Please enter a valid phone number.",
        variant: "destructive",
      })
      return
    }

    const newContact: Contact = {
      id: Date.now().toString(),
      name: newContactName.trim(),
      phone: newContactPhone.trim()
    }

    setPhoneNumbers(prev => [...prev, newContact])
    setNewContactName("")
    setNewContactPhone("")

    // Save to localStorage for future use
    const updatedSaved = [...savedContacts, newContact]
    setSavedContacts(updatedSaved)
    localStorage.setItem('whatsapp-contacts', JSON.stringify(updatedSaved))
  }

  const removeContact = (id: string) => {
    setPhoneNumbers(prev => prev.filter(contact => contact.id !== id))
  }

  const addSavedContact = (contact: Contact) => {
    if (!phoneNumbers.find(c => c.phone === contact.phone)) {
      setPhoneNumbers(prev => [...prev, { ...contact, id: Date.now().toString() }])
    }
  }

  const handleShare = async () => {
    if (phoneNumbers.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please add at least one contact to share with.",
        variant: "destructive",
      })
      return
    }

    if (!message.trim()) {
      toast({
        title: "No Message",
        description: "Please enter a message to send.",
        variant: "destructive",
      })
      return
    }

    setIsSharing(true)
    try {
      await onShare(phoneNumbers.map(c => c.phone), message.trim())
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsSharing(false)
    }
  }

  const handleClose = () => {
    setPhoneNumbers([])
    setNewContactName("")
    setNewContactPhone("")
    setMessage("")
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-green-600" />
            Share via WhatsApp
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Add New Contact */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Add Recipients</Label>
            <div className="grid grid-cols-1 gap-2">
              <Input
                placeholder="Contact name"
                value={newContactName}
                onChange={(e) => setNewContactName(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addContact()}
              />
              <div className="flex gap-2">
                <Input
                  placeholder="Phone number (e.g., +91 9876543210)"
                  value={newContactPhone}
                  onChange={(e) => setNewContactPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addContact()}
                  className="flex-1"
                />
                <Button onClick={addContact} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Saved Contacts */}
          {savedContacts.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center gap-1">
                <Users className="h-4 w-4" />
                Saved Contacts
              </Label>
              <ScrollArea className="h-20">
                <div className="flex flex-wrap gap-1">
                  {savedContacts.map((contact) => (
                    <Badge
                      key={contact.id}
                      variant="outline"
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => addSavedContact(contact)}
                    >
                      {contact.name}
                    </Badge>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Selected Recipients */}
          {phoneNumbers.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Recipients ({phoneNumbers.length})
              </Label>
              <ScrollArea className="h-24 border rounded-md p-2">
                <div className="space-y-1">
                  {phoneNumbers.map((contact) => (
                    <div key={contact.id} className="flex items-center justify-between text-sm">
                      <div>
                        <span className="font-medium">{contact.name}</span>
                        <span className="text-muted-foreground ml-2">{contact.phone}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeContact(contact.id)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          <Separator />

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium">
              Message
            </Label>
            <Textarea
              id="message"
              placeholder="Enter your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isSharing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleShare}
              disabled={isSharing || phoneNumbers.length === 0}
              className="flex-1"
            >
              {isSharing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              {isSharing ? "Sharing..." : "Share Report"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
