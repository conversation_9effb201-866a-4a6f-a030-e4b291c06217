"use client"

import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { VehicleList } from "@/components/vehicle-list"

export function VehicleManagement() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get action from URL parameters, default to "receive"
  const actionFromUrl = searchParams.get('action') || 'receive';
  const [activeTab, setActiveTab] = useState(actionFromUrl);

  // Update tab when URL parameters change (only on initial load)
  useEffect(() => {
    const action = searchParams.get('action');
    if (action && action !== activeTab) {
      setActiveTab(action);
    }
  }, [searchParams, activeTab]);

  // Handle tab change - clear URL parameters when switching tabs manually
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Clear URL parameters when switching tabs manually
    router.replace('/vehicles');
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="receive">Receive</TabsTrigger>
          <TabsTrigger value="load">Load</TabsTrigger>
        </TabsList>
        <TabsContent value="receive" className="space-y-4">
          <VehicleList mode="receive" />
        </TabsContent>
        <TabsContent value="load" className="space-y-4">
          <VehicleList mode="load" />
        </TabsContent>
      </Tabs>
    </div>
  )
}
