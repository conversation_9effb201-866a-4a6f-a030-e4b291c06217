"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestLoadingChartsPage() {
  const [charts, setCharts] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const testAPI = async () => {
    setLoading(true)
    setError("")
    try {
      const response = await fetch('/api/loading-charts?page=1&pageSize=10')
      const data = await response.json()
      
      if (response.ok) {
        setCharts(data.charts || [])
        console.log('API Response:', data)
      } else {
        setError(data.error || 'Failed to fetch loading charts')
      }
    } catch (err: any) {
      setError(err.message || 'Network error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Test Loading Charts API</h1>
      
      <div className="space-y-4">
        <Button onClick={testAPI} disabled={loading}>
          {loading ? "Loading..." : "Test API"}
        </Button>

        {error && (
          <Card>
            <CardContent className="p-4">
              <p className="text-red-500">Error: {error}</p>
            </CardContent>
          </Card>
        )}

        {charts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Loading Charts ({charts.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                {JSON.stringify(charts, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {!loading && !error && charts.length === 0 && (
          <Card>
            <CardContent className="p-4">
              <p className="text-gray-500">No loading charts found. Click "Test API" to fetch data.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
