"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { FileText, Printer, Check, Share2, Download, MessageSquare } from "lucide-react"
import { ReceiptPreview } from "@/components/receipt-preview"
import { Dialog as ReceiptDialog, DialogContent as ReceiptDialogContent, DialogHeader as ReceiptDialogHeader, DialogTitle as ReceiptDialogTitle, DialogDescription as ReceiptDialogDescription } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { Card } from "@/components/ui/card"

interface BookingConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookingId: string
  onClose: () => void
  onGenerateReceipt: () => void
  senderPhone?: string
  recipientPhone?: string
  hideReceiptOption?: boolean
  bookingData?: {
    senderName: string
    recipientName: string
    items: Array<{
      type: string
      count: number
      weight?: string
    }>
    totalWeight?: string
    paymentMode: string
    deliveryType: string
    finalPrice: number
    parcelTypes?: Array<{
      type_id: number
      type_name: string
    }>
  }
}

export function BookingConfirmationDialog({
  open,
  onOpenChange,
  bookingId,
  onClose,
  onGenerateReceipt,
  senderPhone = "",
  recipientPhone = "",
  hideReceiptOption = false,
  bookingData
}: BookingConfirmationDialogProps) {
  const { toast } = useToast()
  const [shareOptions, setShareOptions] = useState({
    whatsapp: true,
    email: false
  })

  // Debug logging to check data
  console.log('BookingConfirmationDialog - bookingData:', bookingData)
  console.log('BookingConfirmationDialog - senderPhone:', senderPhone)
  console.log('BookingConfirmationDialog - recipientPhone:', recipientPhone)
  console.log('BookingConfirmationDialog - bookingData.items:', bookingData?.items)
  console.log('BookingConfirmationDialog - bookingData.senderName:', bookingData?.senderName)
  console.log('BookingConfirmationDialog - bookingData.recipientName:', bookingData?.recipientName)

  const handleGenerateLR = () => {
    // Create a simple LR content
    const lrContent = `
LR COPY
==============================
LR Number: ${bookingId}
Date: ${new Date().toLocaleDateString()}
Sender Phone: ${senderPhone}
Recipient Phone: ${recipientPhone}
==============================
This is your LR copy. Please keep it for reference.
    `;

    // Create a Blob with the LR content
    const blob = new Blob([lrContent], { type: 'text/plain' });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `LR-${bookingId}.txt`;

    // Append the link to the body
    document.body.appendChild(link);

    // Click the link to trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "LR Generated",
      description: "LR has been generated and downloaded.",
    });
  }

  const [showReceiptPreview, setShowReceiptPreview] = useState(false)

  const handleShareViaWhatsApp = () => {
    // For demonstration, show a toast instead of actually opening WhatsApp
    toast({
      title: "Notification Sent",
      description: "Booking confirmation would be sent to sender and recipient via WhatsApp.",
    })

    // Show receipt preview
    setShowReceiptPreview(true)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto overflow-x-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Check className="h-5 w-5 text-primary" />
              Booking Completed Successfully
            </DialogTitle>
            <DialogDescription>
              Your parcel has been booked successfully. LR Number: <span className="font-medium">{bookingId}</span>
            </DialogDescription>
          </DialogHeader>

          <Card className="p-4 bg-muted/30 border-dashed">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">LR Number Pattern</h3>
              <span className="text-xs text-muted-foreground">For reference</span>
            </div>
            <p className="text-xs text-muted-foreground mb-2">
              Format: BRANCH_CODE-YYYYMMDD-XXXX
            </p>
            <div className="text-sm font-mono bg-muted p-2 rounded break-all">
              {bookingId}
            </div>
          </Card>

          <div className="flex flex-col space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              What would you like to do next?
            </p>

            <div className="flex flex-col space-y-2">
              <Button
                variant="outline"
                className="justify-start"
                onClick={handleGenerateLR}
              >
                <Download className="mr-2 h-4 w-4" />
                Generate LR Copy PDF
              </Button>

              

              <Button
                variant="outline"
                className="justify-start"
                onClick={() => {
                  toast({
                    title: "Print Initiated",
                    description: "Sending booking details to printer.",
                  })
                }}
              >
                <Printer className="mr-2 h-4 w-4" />
                Print Booking Details
              </Button>

              
            </div>
          </div>

          <DialogFooter>
            <Button
              onClick={() => {
                if (shareOptions.whatsapp || shareOptions.email) {
                  handleShareViaWhatsApp();
                }
                onClose();
              }}
              className="w-full"
            >
              {(shareOptions.whatsapp || shareOptions.email) ? (
                <>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Send Confirmation & Close
                </>
              ) : (
                "Close"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Receipt Preview Dialog */}
      <ReceiptDialog open={showReceiptPreview} onOpenChange={setShowReceiptPreview}>
        <ReceiptDialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <ReceiptDialogHeader>
            <ReceiptDialogTitle>Receipt Preview</ReceiptDialogTitle>
            <ReceiptDialogDescription>
              You can download, print or share this receipt.
            </ReceiptDialogDescription>
          </ReceiptDialogHeader>

          {bookingData ? (
            <ReceiptPreview
            bookingDetails={{
              id: bookingId,
              date: new Date().toLocaleDateString(),
              amount: bookingData?.finalPrice?.toFixed(2) || "0.00",
              customerName: bookingData?.senderName || "N/A",
              customerPhone: senderPhone || "N/A",
              recipientName: bookingData?.recipientName || "N/A",
              recipientPhone: recipientPhone || "N/A",
              items: (bookingData?.items && bookingData.items.length > 0) ? bookingData.items.map(item => {
                // Find the parcel type name from the type ID
                const parcelType = bookingData.parcelTypes?.find(t => t.type_id.toString() === item.type);
                const typeName = parcelType?.type_name || item.type || "other";

                // Calculate weight - use item weight if available, otherwise use total weight divided by items
                let itemWeight = "0 kg";
                if (item.weight && item.weight !== "") {
                  itemWeight = `${parseFloat(item.weight) * item.count} kg`;
                } else if (bookingData.totalWeight && bookingData.totalWeight !== "") {
                  // If using total weight, divide by total item count
                  const totalItems = bookingData.items.reduce((sum, i) => sum + i.count, 0);
                  const weightPerItem = parseFloat(bookingData.totalWeight) / totalItems;
                  itemWeight = `${(weightPerItem * item.count).toFixed(1)} kg`;
                }

                console.log('Mapping item:', item, 'to:', {
                  name: typeName,
                  quantity: item.count,
                  weight: itemWeight
                });

                return {
                  name: typeName,
                  quantity: item.count,
                  weight: itemWeight
                };
              }) : [{ name: "other", quantity: 1, weight: "0 kg" }],
              totalWeight: bookingData?.totalWeight ? `${bookingData.totalWeight} kg` : undefined,
              paymentMode: bookingData?.paymentMode || "Paid",
              deliveryType: bookingData?.deliveryType || "Standard Delivery"
            }}
          />
          ) : (
            <div className="p-6 text-center">
              <p className="text-muted-foreground">Booking data not available. Please try again.</p>
            </div>
          )}

          <div className="mt-6 text-center">
            <Button
              variant="outline"
              onClick={() => setShowReceiptPreview(false)}
            >
              Close Preview
            </Button>
          </div>
        </ReceiptDialogContent>
      </ReceiptDialog>
    </>
  )
}
