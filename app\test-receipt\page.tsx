"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { BookingConfirmationDialog } from "@/components/booking-confirmation-dialog"
import { ReceiptPreview } from "@/components/receipt-preview"

export default function TestReceiptPage() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [showDirectReceipt, setShowDirectReceipt] = useState(false)

  const testBookingData = {
    senderName: "John Doe",
    recipientName: "<PERSON>",
    items: [
      {
        type: "1",
        count: 3,
        weight: "2.5"
      }
    ],
    totalWeight: "7.5",
    paymentMode: "Paid",
    deliveryType: "Standard Delivery",
    finalPrice: 192.00,
    parcelTypes: [
      {
        type_id: 1,
        type_name: "Electronics"
      },
      {
        type_id: 2,
        type_name: "Documents"
      }
    ]
  }

  const testReceiptData = {
    id: "TTX-20250606-3494",
    date: new Date().toLocaleDateString(),
    amount: "192.00",
    customerName: "<PERSON>",
    customerPhone: "9876543210",
    recipientName: "<PERSON>",
    recipientPhone: "9876543211",
    items: [
      {
        name: "Electronics",
        quantity: 3,
        weight: "7.5 kg"
      }
    ],
    totalWeight: "7.5 kg",
    paymentMode: "Paid",
    deliveryType: "Standard Delivery"
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Test Receipt Preview</h1>

      <div className="space-y-4">
        <Button onClick={() => setIsDialogOpen(true)}>
          Open Test Receipt Dialog
        </Button>

        <Button onClick={() => setShowDirectReceipt(!showDirectReceipt)}>
          {showDirectReceipt ? "Hide" : "Show"} Direct Receipt Preview
        </Button>
      </div>

      {showDirectReceipt && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-4">Direct Receipt Preview</h2>
          <ReceiptPreview bookingDetails={testReceiptData} />
        </div>
      )}

      <BookingConfirmationDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        bookingId="TTX-20250606-3494"
        onClose={() => setIsDialogOpen(false)}
        onGenerateReceipt={() => console.log("Generate receipt clicked")}
        senderPhone="9876543210"
        recipientPhone="9876543211"
        hideReceiptOption={false}
        bookingData={testBookingData}
      />
    </div>
  )
}
