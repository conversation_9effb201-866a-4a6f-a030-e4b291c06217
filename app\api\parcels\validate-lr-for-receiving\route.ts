import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// POST /api/parcels/validate-lr-for-receiving
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { lr_number, chart_ids } = body;

    if (!lr_number || !chart_ids || !Array.isArray(chart_ids)) {
      return NextResponse.json(
        { error: "Missing required parameters: lr_number and chart_ids" },
        { status: 400 },
      );
    }

    // Get user's branch for validation
    const { data: { user } } = await routeHandlerClient.auth.getUser();
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id, role")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the LR is in any of the provided loading charts
    const { data: loadingItems, error: itemsError } = await supabase
      .from("loading_chart_items")
      .select(`
        *,
        chart:loading_charts(
          chart_id,
          chart_number,
          vehicle_id,
          destination_branch_id,
          loading_type,
          vehicle:vehicles(registration_number, vehicle_type),
          destination_branch:branches!destination_branch_id(name, code)
        )
      `)
      .in("chart_id", chart_ids)
      .eq("lr_number", lr_number)
      .eq("status", "Pending");

    if (itemsError) {
      console.error("Error checking loading items:", itemsError);
      return NextResponse.json(
        {
          valid: false,
          message: "Failed to check loading items",
        },
        { status: 500 },
      );
    }

    if (!loadingItems || loadingItems.length === 0) {
      return NextResponse.json(
        {
          valid: false,
          message:
            "LR number not found in any of the loading charts for this vehicle",
        },
        { status: 404 },
      );
    }

    // Get the parcel details
    const { data: parcel, error: parcelError } = await supabase
      .from("parcels")
      .select(`
        *,
        sender_branch:branches!sender_branch_id(name, code),
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    if (parcelError || !parcel) {
      console.error("Error finding parcel:", parcelError);
      return NextResponse.json(
        {
          valid: false,
          message: "Parcel not found",
        },
        { status: 404 },
      );
    }

    // Use the first loading item found (there should typically be only one)
    const loadingItem = loadingItems[0];

    // Return limited parcel information for receiving operators (hide sensitive data)
    return NextResponse.json({
      valid: true,
      parcel: {
        parcel_id: parcel.parcel_id,
        lr_number: parcel.lr_number,
        sender_name: parcel.sender_name,
        recipient_name: parcel.recipient_name,
        sender_branch: (parcel.sender_branch as any)?.name || "Unknown",
        delivery_branch: (parcel.delivery_branch as any)?.name || "Unknown",
        // Hide sensitive information like current_status, number_of_items, etc.
        chart_id: loadingItem.chart_id,
        item_id: loadingItem.item_id,
      },
    });
  } catch (error: any) {
    console.error(
      "Error in POST /api/parcels/validate-lr-for-receiving:",
      error,
    );
    return NextResponse.json(
      {
        valid: false,
        message: "Internal server error",
      },
      { status: 500 },
    );
  }
}
