"use client"

import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CashManagement } from "@/components/cash-management"
import { ExpenseManagement } from "@/components/expense-management"
import { ReportsAnalytics } from "@/components/reports-analytics"
import { PageLayout } from "@/components/page-layout"
import { DashboardShell } from "@/components/dashboard-shell"

export default function OperationsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get tab from URL parameters, default to "cash"
  const tabFromUrl = searchParams.get('tab') || 'cash';
  const [activeTab, setActiveTab] = useState(tabFromUrl);

  // Update tab when URL parameters change (only on initial load)
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tab !== activeTab) {
      setActiveTab(tab);
    }
  }, [searchParams, activeTab]);

  // Handle tab change - clear URL parameters when switching tabs manually
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Clear URL parameters when switching tabs manually
    router.replace('/operations');
  };

  return (
    <PageLayout
      title="Accounts"
      subtitle="Manage cash collections, expenses, and reports"
    >
      <DashboardShell>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-3">
          <TabsList>
            <TabsTrigger value="cash">Cash Management</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>
          <TabsContent value="cash" className="space-y-3">
            <CashManagement />
          </TabsContent>
          <TabsContent value="expenses" className="space-y-3">
            <ExpenseManagement />
          </TabsContent>
          <TabsContent value="reports" className="space-y-3">
            <ReportsAnalytics />
          </TabsContent>
        </Tabs>
      </DashboardShell>
    </PageLayout>
  )
}
