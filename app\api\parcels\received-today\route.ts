import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/received-today
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      console.error("User lookup error:", userError);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    console.log("🔍 DEBUG: User branch ID:", userData.branch_id);

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20");
    const search = url.searchParams.get("search") || "";

    console.log(
      "🔍 DEBUG: Query params - page:",
      page,
      "pageSize:",
      pageSize,
      "search:",
      search,
    );

    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Get today's date in YYYY-MM-DD format
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    console.log("🔍 DEBUG: Fetching parcels received today:", todayStr);

    // Query parcels table for parcels received today at user's branch
    let query = supabase
      .from("parcels")
      .select(
        `
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        recipient_phone,
        sender_phone,
        delivery_branch_id,
        sender_branch_id,
        number_of_items,
        current_status,
        booking_datetime,
        weight,
        total_amount,
        payment_mode,
        item_type,
        sender_branch:branches!parcels_sender_branch_id_fkey(name, code, address),
        delivery_branch:branches!parcels_delivery_branch_id_fkey(name, code, address)
      `,
        { count: "exact" },
      )
      .eq("delivery_branch_id", userData.branch_id)
      .eq("current_status", "To Be Received")
      .gte("booking_datetime", `${todayStr}T00:00:00.000Z`)
      .lt("booking_datetime", `${todayStr}T23:59:59.999Z`)
      .order("booking_datetime", { ascending: false })
      .range(from, to);

    // Add search filter if provided
    if (search) {
      query = query.or(
        `lr_number.ilike.%${search}%,sender_name.ilike.%${search}%,recipient_name.ilike.%${search}%`,
      );
    }

    const { data: parcels, error: parcelsError, count } = await query;

    if (parcelsError) {
      console.error("❌ Error fetching received parcels today:", parcelsError);
      return NextResponse.json({ error: "Failed to fetch parcels" }, {
        status: 500,
      });
    }

    console.log("✅ Successfully fetched received parcels today");
    console.log(
      "🔍 DEBUG: Found",
      count,
      "total parcels,",
      parcels?.length,
      "on this page",
    );
    console.log("🔍 DEBUG: Sample parcel data:", parcels?.[0]);

    return NextResponse.json({
      parcels: parcels || [],
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
      },
    });
  } catch (error: any) {
    console.error("❌ Error in GET /api/parcels/received-today:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
