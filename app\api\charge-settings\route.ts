import { NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

// GET /api/charge-settings
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    const { data: settings, error } = await supabase
      .from('charge_settings')
      .select('*')
      .eq('is_active', true)
      .order('setting_name')

    if (error) {
      console.error('Error fetching charge settings:', error)
      return NextResponse.json({ error: 'Failed to fetch charge settings' }, { status: 500 })
    }

    return NextResponse.json(settings)
  } catch (error: any) {
    console.error('Error in GET /api/charge-settings:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// POST /api/charge-settings
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate required fields
    if (!body.setting_name || !body.setting_type || body.setting_value === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: setting_name, setting_type, setting_value' },
        { status: 400 }
      )
    }

    // Validate setting_type
    if (!['percentage', 'flat'].includes(body.setting_type)) {
      return NextResponse.json(
        { error: 'setting_type must be either "percentage" or "flat"' },
        { status: 400 }
      )
    }

    const { data: setting, error } = await supabase
      .from('charge_settings')
      .insert([{
        setting_name: body.setting_name,
        setting_type: body.setting_type,
        setting_value: body.setting_value,
        description: body.description || null,
        is_active: body.is_active !== undefined ? body.is_active : true
      }])
      .select()
      .single()

    if (error) {
      console.error('Error creating charge setting:', error)
      return NextResponse.json({ error: 'Failed to create charge setting' }, { status: 500 })
    }

    return NextResponse.json(setting, { status: 201 })
  } catch (error: any) {
    console.error('Error in POST /api/charge-settings:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/charge-settings (bulk update)
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    if (!Array.isArray(body.settings)) {
      return NextResponse.json(
        { error: 'Expected an array of settings' },
        { status: 400 }
      )
    }

    const updatedSettings = []

    for (const setting of body.settings) {
      if (!setting.setting_id || setting.setting_value === undefined) {
        continue
      }

      const { data, error } = await supabase
        .from('charge_settings')
        .update({
          setting_value: setting.setting_value,
          description: setting.description,
          is_active: setting.is_active !== undefined ? setting.is_active : true,
          updated_at: new Date().toISOString()
        })
        .eq('setting_id', setting.setting_id)
        .select()
        .single()

      if (error) {
        console.error(`Error updating setting ${setting.setting_id}:`, error)
        continue
      }

      updatedSettings.push(data)
    }

    return NextResponse.json({ 
      message: `Updated ${updatedSettings.length} settings`,
      settings: updatedSettings 
    })
  } catch (error: any) {
    console.error('Error in PUT /api/charge-settings:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
