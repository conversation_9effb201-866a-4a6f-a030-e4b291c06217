# Admin App User Creation Fix

## Problem
When creating new branch users in the admin app, only the auth.users record is created, but the corresponding public.users record is not created. This causes the branch app to fail when trying to determine the user's branch_id.

## Root Cause
- The database uses the OLD schema (user_id as integer, auth_id as UUID)
- But the automatic triggers/edge functions expect the NEW schema (id as UUID primary key)
- This mismatch causes automatic user creation to fail silently

## Solution
The admin app needs to manually create the public.users record after creating the auth user.

## Implementation Steps

### 1. Add User Creation Helper to Admin App
Add this function to the admin app (similar to the one in the branch app):

```javascript
// In admin app: lib/admin-user-helpers.js
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

export async function createBranchUser(email, password, name, role, branchId) {
  try {
    console.log('Creating branch user:', { email, name, role, branchId })
    
    // Step 1: Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: name,
        role: role
      }
    })
    
    if (authError) {
      console.error('Error creating auth user:', authError)
      throw new Error(`Failed to create auth user: ${authError.message}`)
    }
    
    console.log('Auth user created:', authData.user.id)
    
    // Step 2: Create public user record
    const { data: publicUser, error: publicError } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        email: email,
        name: name,
        role: role,
        branch_id: branchId,
        two_fa_enabled: false,
        employee_type: 'Company'
      })
      .select()
      .single()
    
    if (publicError) {
      console.error('Error creating public user:', publicError)
      
      // If public user creation fails, we should delete the auth user
      try {
        await supabase.auth.admin.deleteUser(authData.user.id)
        console.log('Cleaned up auth user after public user creation failed')
      } catch (cleanupError) {
        console.error('Failed to cleanup auth user:', cleanupError)
      }
      
      throw new Error(`Failed to create user profile: ${publicError.message}`)
    }
    
    console.log('Public user created:', publicUser)
    
    return {
      authUser: authData.user,
      publicUser: publicUser
    }
    
  } catch (error) {
    console.error('Error in createBranchUser:', error)
    throw error
  }
}
```

### 2. Update Admin App User Creation Form
Replace the current user creation logic with:

```javascript
// In admin app user creation component
import { createBranchUser } from '@/lib/admin-user-helpers'

async function handleCreateUser(formData) {
  try {
    setIsLoading(true)
    
    const result = await createBranchUser(
      formData.email,
      formData.password,
      formData.name,
      formData.role,
      formData.branchId
    )
    
    toast({
      title: "Success",
      description: `User ${formData.name} created successfully for branch ${formData.branchId}`,
    })
    
    // Refresh user list or redirect
    
  } catch (error) {
    toast({
      title: "Error",
      description: error.message,
      variant: "destructive"
    })
  } finally {
    setIsLoading(false)
  }
}
```

### 3. Add Validation Function
Add this function to check if a user was created properly:

```javascript
export async function validateUserCreation(authId) {
  try {
    // Check if public user exists
    const { data: publicUser, error } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', authId)
      .single()
    
    if (error || !publicUser) {
      console.error('Public user not found for auth_id:', authId)
      return false
    }
    
    console.log('User validation passed:', publicUser)
    return true
    
  } catch (error) {
    console.error('Error validating user:', error)
    return false
  }
}
```

## Testing the Fix

1. Create a new user in the admin app
2. Check that both auth.users and public.users records are created
3. Test login in the branch app
4. Verify that branch operations work correctly

## Prevention

To prevent this issue in the future:

1. **Always use the createBranchUser helper** when creating users in admin app
2. **Add validation** after user creation to ensure both records exist
3. **Fix the database schema** to be consistent (either all OLD or all NEW schema)
4. **Update the triggers/edge functions** to work with the current schema

## Emergency Fix for Existing Users

If you find more users with missing public.users records, use this script:

```javascript
// emergency-fix-users.js
const missingAuthIds = [
  // Add any missing auth_ids here
]

for (const authId of missingAuthIds) {
  // Find the user in auth.users and create corresponding public.users record
  // (Implementation similar to what we did for the SALEM HO user)
}
```
