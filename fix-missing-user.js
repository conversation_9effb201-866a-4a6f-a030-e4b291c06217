const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://nekjeqxlwhfwyekeinnc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y'
);

async function fixMissingUser() {
  try {
    console.log('Fixing missing user record...');
    
    // The missing auth_id from the error
    const missingAuthId = 'c18a88ae-8e04-4576-b960-06ca18dd8f1a';
    
    console.log('Missing auth_id:', missingAuthId);
    
    // We need to get the email from somewhere. Let's check if we can find it in the error logs
    // or we need to ask the user for the email of this auth_id
    
    // For now, let's try to create a user record with minimal information
    // The user will need to provide the email and other details
    
    console.log('\nTo fix this issue, we need to create a user record in the public.users table.');
    console.log('We need the following information for auth_id:', missingAuthId);
    console.log('- Email address');
    console.log('- Name');
    console.log('- Role (Manager, Admin, etc.)');
    console.log('- Branch ID (if known)');
    
    console.log('\nOnce you have this information, you can run the following SQL in Supabase SQL Editor:');
    console.log(`
INSERT INTO public.users (
  auth_id,
  email,
  name,
  role,
  branch_id,
  two_fa_enabled
) VALUES (
  '${missingAuthId}',
  'USER_EMAIL_HERE',  -- Replace with actual email
  'USER_NAME_HERE',   -- Replace with actual name
  'Manager',          -- Replace with actual role
  BRANCH_ID_HERE,     -- Replace with actual branch_id (integer)
  false
);
`);

    console.log('\nAlternatively, if you know the email, we can try to find existing user data:');
    
    // Let's also check if there's a user with null auth_id that might be this user
    const { data: usersWithoutAuthId, error } = await supabase
      .from('users')
      .select('*')
      .is('auth_id', null);
      
    if (error) {
      console.error('Error checking users without auth_id:', error);
    } else {
      console.log('\nUsers without auth_id (might be the missing user):');
      usersWithoutAuthId.forEach(user => {
        console.log(`- ${user.email}: user_id=${user.user_id}, branch_id=${user.branch_id}, role=${user.role}`);
      });
      
      if (usersWithoutAuthId.length > 0) {
        console.log('\nIf one of these is the missing user, you can update their auth_id:');
        console.log(`
UPDATE public.users 
SET auth_id = '${missingAuthId}' 
WHERE email = 'CORRECT_EMAIL_HERE';
`);
      }
    }
    
  } catch (err) {
    console.error('Error:', err);
  }
}

fixMissingUser();
