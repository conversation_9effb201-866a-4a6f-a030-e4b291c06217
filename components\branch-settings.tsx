"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Building2, Clock, Calendar, IndianRupee, Webhook, Database, Shield } from "lucide-react"

const branchFormSchema = z.object({
  name: z.string().min(2, "Branch name must be at least 2 characters"),
  code: z.string().min(2, "Branch code must be at least 2 characters"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  email: z.string().email("Invalid email address"),
  operatingHours: z.object({
    weekday: z.object({
      start: z.string(),
      end: z.string(),
    }),
    weekend: z.object({
      start: z.string(),
      end: z.string(),
    }),
  }),
  isActive: z.boolean(),
})

export function BranchSettings() {
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  
  const form = useForm<z.infer<typeof branchFormSchema>>({
    resolver: zodResolver(branchFormSchema),
    defaultValues: {
      name: "Chennai Central",
      code: "CHE001",
      address: "45, Anna Salai, Chennai - 600002",
      phone: "+91 44 2345 6789",
      email: "<EMAIL>",
      operatingHours: {
        weekday: {
          start: "09:00",
          end: "18:00",
        },
        weekend: {
          start: "10:00",
          end: "16:00",
        },
      },
      isActive: true,
    },
  })

  function onSubmit(values: z.infer<typeof branchFormSchema>) {
    toast({
      title: "Settings Updated",
      description: "Branch settings have been updated successfully.",
    })
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Branch Settings</h2>
        <p className="text-muted-foreground">
          Configure branch details, operating hours, and system preferences
        </p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="backup">Backup & Recovery</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>Branch Information</CardTitle>
              <CardDescription>
                Manage your branch details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Branch Name</FormLabel>
                          <FormControl>
                            <Input disabled={!isEditing} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Branch Code</FormLabel>
                          <FormControl>
                            <Input disabled={!isEditing} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <Textarea disabled={!isEditing} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input disabled={!isEditing} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input disabled={!isEditing} type="email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Branch Status</FormLabel>
                          <FormDescription>
                            Activate or deactivate branch operations
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            disabled={!isEditing}
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {isEditing ? (
                    <div className="flex justify-end space-x-2">
                      <Button 
                        type="button" 
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                      >
                        Cancel
                      </Button>
                      <Button type="submit">Save Changes</Button>
                    </div>
                  ) : (
                    <Button 
                      type="button"
                      onClick={() => setIsEditing(true)}
                    >
                      Edit Settings
                    </Button>
                  )}
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <CardTitle>Operating Hours</CardTitle>
                </div>
                <CardDescription>Set branch working hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Weekdays</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <FormLabel>Start Time</FormLabel>
                        <Input type="time" defaultValue="09:00" />
                      </div>
                      <div>
                        <FormLabel>End Time</FormLabel>
                        <Input type="time" defaultValue="18:00" />
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-2">Weekends</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <FormLabel>Start Time</FormLabel>
                        <Input type="time" defaultValue="10:00" />
                      </div>
                      <div>
                        <FormLabel>End Time</FormLabel>
                        <Input type="time" defaultValue="16:00" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <CardTitle>Holiday Calendar</CardTitle>
                </div>
                <CardDescription>Manage branch holidays</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full">
                    Add Holiday
                  </Button>
                  <div className="space-y-2">
                    <p className="text-sm">Upcoming Holidays:</p>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>Apr 14, 2024 - Tamil New Year</li>
                      <li>Aug 15, 2024 - Independence Day</li>
                      <li>Oct 02, 2024 - Gandhi Jayanti</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <IndianRupee className="h-4 w-4" />
                  <CardTitle>Rate Card</CardTitle>
                </div>
                <CardDescription>Configure service rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full">
                    Manage Rates
                  </Button>
                  <div className="space-y-2">
                    <p className="text-sm">Current Rates:</p>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li>Standard Delivery - ₹50/kg</li>
                      <li>Express Delivery - ₹100/kg</li>
                      <li>Same Day Delivery - ₹150/kg</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="integrations">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Webhook className="h-4 w-4" />
                  <CardTitle>API Configuration</CardTitle>
                </div>
                <CardDescription>Manage API keys and webhooks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <h4 className="text-sm font-medium">API Key</h4>
                    <div className="mt-2 flex items-center space-x-2">
                      <Input
                        type="password"
                        value="sk_test_123456789"
                        readOnly
                      />
                      <Button variant="outline" size="sm">
                        Copy
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Webhook URLs</h4>
                    <Input placeholder="https://your-webhook-url.com" />
                    <Button variant="outline" className="w-full">
                      Add Webhook
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <CardTitle>Security Settings</CardTitle>
                </div>
                <CardDescription>Configure security preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <label className="text-sm font-medium">Two-Factor Authentication</label>
                      <p className="text-xs text-muted-foreground">
                        Require 2FA for all users
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <label className="text-sm font-medium">Session Timeout</label>
                      <p className="text-xs text-muted-foreground">
                        Auto logout after inactivity
                      </p>
                    </div>
                    <Select defaultValue="30">
                      <SelectTrigger className="w-[100px]">
                        <SelectValue placeholder="Select time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 mins</SelectItem>
                        <SelectItem value="30">30 mins</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="backup">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <CardTitle>Data Backup</CardTitle>
                </div>
                <CardDescription>Configure backup settings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <label className="text-sm font-medium">Auto Backup</label>
                      <p className="text-xs text-muted-foreground">
                        Automatically backup data
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Select defaultValue="daily">
                    <SelectTrigger>
                      <SelectValue placeholder="Backup frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" className="w-full">
                    Backup Now
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Backup History</CardTitle>
                <CardDescription>View and restore backups</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <p className="text-sm">March 21, 2024 10:30 AM</p>
                      <Button variant="outline" size="sm">Restore</Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm">March 20, 2024 10:30 AM</p>
                      <Button variant="outline" size="sm">Restore</Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm">March 19, 2024 10:30 AM</p>
                      <Button variant="outline" size="sm">Restore</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}