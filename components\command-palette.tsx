"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Command } from "cmdk"
import { Dialog, DialogTitle } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import {
  Package,
  Truck,
  FileText,
  BarChart3,
  Users,
  Settings,
  IndianRupee,
  Search,
  X
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

const searchItems = [
  {
    heading: "Main Navigation",
    items: [
      {
        title: "Dashboard",
        href: "/",
        icon: Package,
        keywords: "home overview summary stats",
      },
      {
        title: "Parcels",
        href: "/parcels",
        icon: Package,
        keywords: "shipments packages delivery tracking",
      },
      {
        title: "Load & Receive",
        href: "/load-receive",
        icon: Truck,
        keywords: "vehicles transport trucks fleet logistics loading receiving",
      },
      {
        title: "Memos",
        href: "/memos",
        icon: FileText,
        keywords: "memos notes documents communications",
      },
      {
        title: "Accounts",
        href: "/operations",
        icon: IndianRupee,
        keywords: "accounts operations finance cash management",
      },
    ],
  },
  {
    heading: "Accounts",
    items: [
      {
        title: "Expenses",
        href: "/operations?tab=expenses",
        icon: FileText,
        keywords: "costs bills payments finance accounting",
      },
      {
        title: "Reports",
        href: "/operations?tab=reports",
        icon: BarChart3,
        keywords: "analytics statistics data insights",
      },
    ],
  },
  {
    heading: "Administration",
    items: [
      {
        title: "Users",
        href: "/users",
        icon: Users,
        keywords: "staff employees accounts permissions",
      },
      {
        title: "Profile and Settings",
        href: "/settings",
        icon: Settings,
        keywords: "configuration preferences system branch profile",
      },
    ],
  },
]

export function CommandPalette() {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "j" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const handleSelect = (href: string) => {
    setOpen(false)
    router.push(href)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogPrimitive.Content className={cn(
          "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg overflow-hidden p-0"
        )}>
          <DialogTitle className="sr-only">Search</DialogTitle>
          <div className="relative">
            {/* Custom close button to avoid overlap */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-2 h-6 w-6 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-10"
              onClick={() => setOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>

          <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
            <div className="flex items-center border-b px-3 pr-10" cmdk-input-wrapper="">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Command.Input
                value={search}
                onValueChange={setSearch}
                placeholder="Search features and pages..."
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100 ml-auto mr-8">
                <span className="text-xs">⌘</span>J
              </kbd>
            </div>
            <Command.List className="max-h-[300px] overflow-y-auto overflow-x-hidden">
              <Command.Empty className="py-6 text-center text-sm">
                No results found.
              </Command.Empty>
              {searchItems.map((group) => (
                <Command.Group key={group.heading} heading={group.heading}>
                  {group.items.map((item) => {
                    const Icon = item.icon
                    return (
                      <Command.Item
                        key={item.href}
                        value={`${item.title} ${item.keywords}`}
                        onSelect={() => handleSelect(item.href)}
                        onClick={() => handleSelect(item.href)}
                        className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground transition-colors"
                      >
                        <Icon className="mr-2 h-4 w-4" />
                        <span>{item.title}</span>
                      </Command.Item>
                    )
                  })}
                </Command.Group>
              ))}
            </Command.List>
          </Command>
        </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
  )
}
