"use client"

import { ReactNode } from "react"

interface DiagonalHeroProps {
  title: string
  subtitle?: string
  children?: ReactNode
  showPattern?: boolean
}

export function DiagonalHero({
  title,
  subtitle,
  children,
  showPattern = true
}: DiagonalHeroProps) {
  return (
    <div className="relative overflow-hidden bg-primary text-white">
      {/* Diagonal Yellow Element */}
      <div className="absolute -left-20 -top-40 h-96 w-[800px] rotate-12 transform bg-secondary" />

      {/* Background Circles */}
      <div className="absolute right-10 top-10 h-32 w-32 rounded-full bg-accent/20" />
      <div className="absolute left-1/3 bottom-20 h-16 w-16 rounded-full bg-secondary/20" />
      <div className="absolute right-1/4 top-1/2 h-24 w-24 rounded-full bg-accent/10" />

      {/* Content */}
      <div className="container relative z-10 px-4 py-16 md:py-24">
        <div className="max-w-3xl">
          <h1 className="mb-4 text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl">
            {title}
          </h1>
          {subtitle && (
            <p className="text-xl text-white/80 md:text-2xl">
              {subtitle}
            </p>
          )}
        </div>

        {children && (
          <div className="mt-8">
            {children}
          </div>
        )}
      </div>

      {/* Bottom Wave */}
      {showPattern && (
        <div className="absolute bottom-0 left-0 right-0">
          <svg
            viewBox="0 0 1440 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full"
          >
            <path
              d="M0 0L48 8.875C96 17.75 192 35.5 288 44.375C384 53.25 480 53.25 576 44.375C672 35.5 768 17.75 864 26.625C960 35.5 1056 71 1152 80.875C1248 90.75 1344 71 1392 62.125L1440 53.25V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0V0Z"
              fill="white"
            />
          </svg>
        </div>
      )}
    </div>
  )
}
