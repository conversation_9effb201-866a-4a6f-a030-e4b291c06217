-- Parcel Management System Redesign Migration
-- This migration implements the new parcel status system and actions tracking

-- Step 1: Update parcel_status enum to new values
DO $$
BEGIN
  -- First, add new enum values if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_enum e
    JOIN pg_type t ON e.enumtypid = t.oid
    WHERE t.typname = 'parcel_status' AND e.enumlabel = 'Loaded'
  ) THEN
    ALTER TYPE parcel_status ADD VALUE 'Loaded' AFTER 'Booked';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_enum e
    JOIN pg_type t ON e.enumtypid = t.oid
    WHERE t.typname = 'parcel_status' AND e.enumlabel = 'Received'
  ) THEN
    ALTER TYPE parcel_status ADD VALUE 'Received' AFTER 'Loaded';
  END IF;
END$$;

-- Step 2: Create new parcel_actions table to replace multiple tracking tables
CREATE TABLE IF NOT EXISTS public.parcel_actions (
  action_id                 SERIAL PRIMARY KEY,
  parcel_id                 INT NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
  action_type               VARCHAR(50) NOT NULL, -- 'Booked', 'Loaded', 'Received', 'Delivered'
  action_timestamp          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Location and branch information
  branch_id                 INT REFERENCES branches(branch_id) ON DELETE SET NULL,
  location_name             VARCHAR(255),
  
  -- Vehicle and operator information
  vehicle_id                INT REFERENCES vehicles(vehicle_id) ON DELETE SET NULL,
  operator_id               INT REFERENCES users(user_id) ON DELETE SET NULL,
  
  -- Loading/Receiving specific fields
  destination_branch_id     INT REFERENCES branches(branch_id) ON DELETE SET NULL,
  loading_type              VARCHAR(20), -- 'Direct' or 'Via'
  quantity_loaded           INT,
  quantity_received         INT,
  
  -- Additional details
  remarks                   TEXT,
  reference_number          VARCHAR(100), -- Loading chart number, receipt number, etc.
  
  -- Metadata
  created_by                UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at                TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS parcel_actions_parcel_id_idx ON parcel_actions(parcel_id);
CREATE INDEX IF NOT EXISTS parcel_actions_action_type_idx ON parcel_actions(action_type);
CREATE INDEX IF NOT EXISTS parcel_actions_timestamp_idx ON parcel_actions(action_timestamp);
CREATE INDEX IF NOT EXISTS parcel_actions_branch_id_idx ON parcel_actions(branch_id);
CREATE INDEX IF NOT EXISTS parcel_actions_vehicle_id_idx ON parcel_actions(vehicle_id);

-- Step 3: Create new loading_type enum for Direct/Via classification
CREATE TYPE loading_type AS ENUM ('Direct', 'Via');

-- Step 4: Modify loading_charts table to support destination selection and remove memo dependency
ALTER TABLE loading_charts 
ADD COLUMN IF NOT EXISTS loading_type loading_type,
ADD COLUMN IF NOT EXISTS destination_city_id INT REFERENCES cities(city_id) ON DELETE SET NULL;

-- Make memo_id nullable since loading operations will be independent of memos
ALTER TABLE loading_charts 
ALTER COLUMN memo_id DROP NOT NULL;

-- Step 5: Add comments for documentation
COMMENT ON TABLE parcel_actions IS 'Unified table to track all parcel actions and movements, replacing multiple tracking tables';
COMMENT ON COLUMN parcel_actions.action_type IS 'Type of action: Booked, Loaded, Received, Delivered';
COMMENT ON COLUMN parcel_actions.loading_type IS 'For Load actions: Direct (parcel destination = loading destination) or Via (intermediate stop)';
COMMENT ON COLUMN parcel_actions.destination_branch_id IS 'For Load actions: the branch where parcels are being loaded to';
COMMENT ON COLUMN parcel_actions.quantity_loaded IS 'Number of items loaded (for Load actions)';
COMMENT ON COLUMN parcel_actions.quantity_received IS 'Number of items received (for Receive actions)';

-- Step 6: Create function to automatically create parcel actions when status changes
CREATE OR REPLACE FUNCTION create_parcel_action_on_status_change()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_name VARCHAR(255);
BEGIN
  -- Only create action if status actually changed
  IF OLD.current_status IS DISTINCT FROM NEW.current_status THEN
    
    -- Get branch name for location
    SELECT name INTO v_branch_name 
    FROM branches 
    WHERE branch_id = CASE 
      WHEN NEW.current_status = 'Booked' THEN NEW.sender_branch_id
      WHEN NEW.current_status IN ('Loaded', 'Received') THEN NEW.delivery_branch_id -- Will be updated by loading/receiving operations
      WHEN NEW.current_status = 'Delivered' THEN NEW.delivery_branch_id
      ELSE NEW.sender_branch_id
    END;
    
    -- Insert action record
    INSERT INTO parcel_actions (
      parcel_id,
      action_type,
      action_timestamp,
      branch_id,
      location_name,
      remarks
    ) VALUES (
      NEW.parcel_id,
      NEW.current_status,
      NOW(),
      CASE 
        WHEN NEW.current_status = 'Booked' THEN NEW.sender_branch_id
        WHEN NEW.current_status IN ('Loaded', 'Received') THEN NEW.delivery_branch_id
        WHEN NEW.current_status = 'Delivered' THEN NEW.delivery_branch_id
        ELSE NEW.sender_branch_id
      END,
      v_branch_name,
      'Status updated to ' || NEW.current_status
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic action creation
DROP TRIGGER IF EXISTS parcel_status_change_trigger ON parcels;
CREATE TRIGGER parcel_status_change_trigger
  AFTER UPDATE OF current_status ON parcels
  FOR EACH ROW
  EXECUTE FUNCTION create_parcel_action_on_status_change();

-- Step 7: Migrate existing data from parcel_status_history to parcel_actions
INSERT INTO parcel_actions (
  parcel_id,
  action_type,
  action_timestamp,
  branch_id,
  location_name,
  remarks,
  created_by
)
SELECT 
  psh.parcel_id,
  psh.status::VARCHAR(50),
  psh.timestamp,
  psh.branch_id,
  psh.location,
  psh.remarks,
  psh.updated_by
FROM parcel_status_history psh
WHERE NOT EXISTS (
  SELECT 1 FROM parcel_actions pa 
  WHERE pa.parcel_id = psh.parcel_id 
  AND pa.action_type = psh.status::VARCHAR(50)
  AND pa.action_timestamp = psh.timestamp
);
