"use client"

import { useState } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Search, Filter, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

// Demo data
const activityLogs = [
  {
    id: "1",
    timestamp: "2024-03-21T10:30:00",
    user: {
      name: "<PERSON><PERSON>",
      role: "Booking Operator",
    },
    action: "parcel_created",
    details: "Created new parcel booking #LRN123456",
    category: "booking",
  },
  {
    id: "2",
    timestamp: "2024-03-21T11:15:00",
    user: {
      name: "<PERSON><PERSON>",
      role: "Loading Operator",
    },
    action: "vehicle_loaded",
    details: "Completed loading for vehicle TN01BR5678",
    category: "vehicle",
  },
  {
    id: "3",
    timestamp: "2024-03-21T12:00:00",
    user: {
      name: "John Doe",
      role: "Branch Supervisor",
    },
    action: "expense_approved",
    details: "Approved expense claim #EXP789 for ₹4,500",
    category: "expense",
  },
  {
    id: "4",
    timestamp: "2024-03-21T13:30:00",
    user: {
      name: "Senthil Kumar",
      role: "Vehicle Coordinator",
    },
    action: "vehicle_departed",
    details: "Marked vehicle TN02BR1234 as departed",
    category: "vehicle",
  },
]

const actionCategories = {
  all: "All Activities",
  booking: "Bookings",
  vehicle: "Vehicles",
  expense: "Expenses",
  user: "User Management",
}

const actionColors = {
  parcel_created: "bg-green-500",
  vehicle_loaded: "bg-blue-500",
  expense_approved: "bg-purple-500",
  vehicle_departed: "bg-yellow-500",
}

export function ActivityLog() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Activity Log</h2>
        <p className="text-muted-foreground">
          Track and monitor all branch operations and user actions
        </p>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search activities..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Select
            value={selectedCategory}
            onValueChange={setSelectedCategory}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(actionCategories).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export Log
        </Button>
      </div>

      <div className="grid gap-4">
        {activityLogs.map((log) => (
          <Card key={log.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle className="text-base font-medium">
                  {log.user.name}
                </CardTitle>
                <CardDescription className="flex items-center space-x-2">
                  <span>{log.user.role}</span>
                  <span>•</span>
                  <span>{format(new Date(log.timestamp), "h:mm a")}</span>
                </CardDescription>
              </div>
              <Badge className={actionColors[log.action as keyof typeof actionColors]}>
                {log.action.split("_").map(word => 
                  word.charAt(0).toUpperCase() + word.slice(1)
                ).join(" ")}
              </Badge>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {log.details}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}