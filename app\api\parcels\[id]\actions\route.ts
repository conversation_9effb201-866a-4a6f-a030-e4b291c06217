import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/parcels/[id]/actions
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid parcel ID" }, { status: 400 });
    }

    console.log(`Fetching parcel actions for parcel ${id}`);

    // Get all actions for the parcel from parcel_actions table
    const { data: actions, error: actionsError } = await supabase
      .from("parcel_actions")
      .select(`
        action_id,
        action_type,
        action_timestamp,
        branch_id,
        location_name,
        vehicle_id,
        vehicle_number,
        chart_number,
        loading_type,
        destination_branch_id,
        quantity,
        quantity_loaded,
        quantity_received,
        remarks,
        reference_number,
        created_by
      `)
      .eq("parcel_id", id)
      .order("action_timestamp", { ascending: true });

    if (actionsError) {
      console.error("Error fetching parcel actions:", actionsError);
      console.error("Error details:", JSON.stringify(actionsError, null, 2));
      return NextResponse.json(
        {
          error: "Failed to fetch parcel actions",
          details: actionsError.message,
        },
        { status: 500 },
      );
    }

    console.log(`Found ${actions?.length || 0} actions for parcel ${id}`);

    // Get unique branch IDs to fetch their names
    const branchIds = [
      ...new Set(
        actions?.flatMap((a) =>
          [a.branch_id, a.destination_branch_id].filter(Boolean)
        ) || [],
      ),
    ];

    // Fetch branch names
    const { data: branches } = await supabase
      .from("branches")
      .select("branch_id, name")
      .in("branch_id", branchIds);

    // Create lookup map for branches
    const branchMap = new Map(
      branches?.map((b) => [b.branch_id, b.name]) || [],
    );

    // Process actions to create timeline and detailed views
    const processedActions = actions?.map((action) => ({
      ...action,
      branch_name: branchMap.get(action.branch_id) || action.location_name,
      destination_branch_name: branchMap.get(action.destination_branch_id),
      // vehicle_number and chart_number are already included from the database
    })) || [];

    // Get parcel information to determine actual status changes
    const { data: parcel } = await supabase
      .from("parcels")
      .select("number_of_items, delivery_branch_id")
      .eq("parcel_id", id)
      .single();

    // Create timeline view (only actual status changes)
    const statusChangeActions = [];

    // Track quantities to determine when status actually changed
    let totalReceivedAtDestination = 0;
    let hasBeenLoaded = false;

    for (const action of processedActions) {
      if (action.action_type === "Booked") {
        // Always include booking - this is the initial status
        statusChangeActions.push(action);
      } else if (action.action_type === "Loaded" && !hasBeenLoaded) {
        // Include first loading action only - this changes status from Booked to Loaded
        statusChangeActions.push(action);
        hasBeenLoaded = true;
      } else if (action.action_type === "Received") {
        // Only include if this action resulted in all items being received at destination
        if (action.branch_id === parcel?.delivery_branch_id) {
          totalReceivedAtDestination += action.quantity_received || 0;

          // Only add to timeline if this action completed the receiving (all items received)
          if (totalReceivedAtDestination >= (parcel?.number_of_items || 0)) {
            statusChangeActions.push(action);
          }
        }
      } else if (action.action_type === "Delivered") {
        // Always include delivery - this is the final status
        statusChangeActions.push(action);
      }
    }

    return NextResponse.json({
      timeline: statusChangeActions,
      detailed: processedActions,
      total_actions: processedActions.length,
    });
  } catch (error: any) {
    console.error(`Error in GET /api/parcels/${params.id}/actions:`, error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
