"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Search, Filter, CalendarIcon, X, Loader2, ChevronLeft, ChevronRight, Truck, Package, MapPin, Clock } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { LoadingChartDetailsDialog } from "@/components/loading-chart-details-dialog"
import { useToast } from "@/hooks/use-toast"
import { useSupabase } from "@/contexts/supabase-provider"
import { getUserBranchIdAsync } from "@/lib/branch-utils"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pa<PERSON>ationItem,
  PaginationLink,
  PaginationNext,
  Pa<PERSON>ationPrevious,
} from "@/components/ui/pagination"

interface LoadingChart {
  chart_id: number
  chart_number: string
  vehicle_id: number
  destination_branch_id: number
  created_at: string
  status: string
  notes?: string
  loading_type?: string
  total_parcels?: number
  loaded_parcels?: number
  vehicle?: {
    registration_number: string
    vehicle_type: string
  }
  destination_branch?: {
    name: string
    code: string
  }
  memo?: {
    memo_number: string
  }
}

interface Branch {
  branch_id: number
  name: string
  code: string
}

interface Vehicle {
  vehicle_id: number
  registration_number: string
  vehicle_type: string
}

const statusColors = {
  "Created": "bg-blue-500",
  "In Transit": "bg-yellow-500", 
  "Completed": "bg-green-500",
  "Cancelled": "bg-red-500"
}

const statusLabels = {
  "Created": "Created",
  "In Transit": "In Transit",
  "Completed": "Completed", 
  "Cancelled": "Cancelled"
}

export function LoadingChartView() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedChart, setSelectedChart] = useState<LoadingChart | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedVehicle, setSelectedVehicle] = useState("all")
  const [selectedDestination, setSelectedDestination] = useState("all")
  const [loadingCharts, setLoadingCharts] = useState<LoadingChart[]>([])
  const [loading, setLoading] = useState(true)
  const [branchList, setBranchList] = useState<Branch[]>([])
  const [vehicleList, setVehicleList] = useState<Vehicle[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 20,
    totalItems: 0,
    totalPages: 1
  })
  const [userBranchId, setUserBranchId] = useState<number | null>(null)
  const { toast } = useToast()
  const { supabase, session } = useSupabase()

  // Get user branch ID
  useEffect(() => {
    const fetchUserBranchId = async () => {
      try {
        const branchId = await getUserBranchIdAsync()
        setUserBranchId(branchId)
      } catch (error) {
        console.error('Error fetching user branch ID:', error)
        toast({
          title: "Authentication Issue",
          description: "Could not determine your branch. Please try logging in again.",
          variant: "destructive",
        })
      }
    }

    fetchUserBranchId()
  }, [session, toast])

  // Fetch loading charts with pagination and filters
  const fetchLoadingCharts = async (page: number = 1) => {
    setLoading(true)
    try {
      const url = new URL('/api/loading-charts', window.location.origin)
      url.searchParams.append('page', page.toString())
      url.searchParams.append('pageSize', pagination.pageSize.toString())

      // Add filters
      if (selectedStatus !== "all") {
        url.searchParams.append('status', selectedStatus)
      }
      if (selectedVehicle !== "all") {
        url.searchParams.append('vehicle_id', selectedVehicle)
      }
      if (selectedDestination !== "all") {
        url.searchParams.append('destination_branch_id', selectedDestination)
      }
      if (selectedDate) {
        url.searchParams.append('date', format(selectedDate, 'yyyy-MM-dd'))
      }
      if (searchQuery) {
        url.searchParams.append('search', searchQuery)
      }

      const response = await fetch(url.toString())
      if (!response.ok) {
        throw new Error('Failed to fetch loading charts')
      }

      const data = await response.json()
      setLoadingCharts(Array.isArray(data.charts) ? data.charts : [])
      setPagination({
        currentPage: data.pagination?.page || 1,
        pageSize: data.pagination?.pageSize || 20,
        totalItems: data.pagination?.total || 0,
        totalPages: data.pagination?.totalPages || 1
      })
    } catch (error: any) {
      console.error('Error fetching loading charts:', error)
      toast({
        title: 'Error',
        description: 'Failed to load loading charts',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch reference data (branches and vehicles)
  const fetchReferenceData = async () => {
    try {
      // Fetch branches
      const branchesResponse = await fetch('/api/branches')
      if (branchesResponse.ok) {
        const branchesData = await branchesResponse.json()
        setBranchList(Array.isArray(branchesData) ? branchesData : [])
      } else {
        console.warn('Failed to fetch branches')
        setBranchList([])
      }

      // Fetch vehicles
      const vehiclesResponse = await fetch('/api/vehicles')
      if (vehiclesResponse.ok) {
        const vehiclesData = await vehiclesResponse.json()
        setVehicleList(Array.isArray(vehiclesData.vehicles) ? vehiclesData.vehicles : [])
      } else {
        console.warn('Failed to fetch vehicles')
        setVehicleList([])
      }
    } catch (error: any) {
      console.error('Error fetching reference data:', error)
      // Ensure arrays are set even on error
      setBranchList([])
      setVehicleList([])
      // Don't show error toast for reference data as it's not critical
    }
  }

  // Initial data loading
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchReferenceData()
      await fetchLoadingCharts(1)
    }

    loadInitialData()
  }, [])

  // Fetch loading charts when filters change
  useEffect(() => {
    if (!loading) {
      fetchLoadingCharts(1) // Reset to first page when filters change
    }
  }, [selectedStatus, selectedVehicle, selectedDestination, selectedDate, searchQuery])

  const handleChartClick = (chart: LoadingChart) => {
    setSelectedChart(chart)
    setIsDetailsOpen(true)
  }

  const handlePageChange = (page: number) => {
    fetchLoadingCharts(page)
  }

  const clearFilters = () => {
    setSelectedStatus("all")
    setSelectedVehicle("all")
    setSelectedDestination("all")
    setSelectedDate(undefined)
    setSearchQuery("")
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Loading Charts</h2>
          <p className="text-muted-foreground">
            View and manage parcels organized by loading charts
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by chart number or LR number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Created">Created</SelectItem>
                <SelectItem value="In Transit">In Transit</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            {/* Vehicle Filter */}
            <Select value={selectedVehicle} onValueChange={setSelectedVehicle}>
              <SelectTrigger>
                <SelectValue placeholder="All Vehicles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Vehicles</SelectItem>
                {Array.isArray(vehicleList) && vehicleList.map((vehicle) => (
                  <SelectItem key={vehicle.vehicle_id} value={vehicle.vehicle_id.toString()}>
                    {vehicle.registration_number} ({vehicle.vehicle_type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Destination Filter */}
            <Select value={selectedDestination} onValueChange={setSelectedDestination}>
              <SelectTrigger>
                <SelectValue placeholder="All Destinations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Destinations</SelectItem>
                {Array.isArray(branchList) && branchList.map((branch) => (
                  <SelectItem key={branch.branch_id} value={branch.branch_id.toString()}>
                    {branch.name} ({branch.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Date Filter */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Clear Filters */}
          <div className="flex justify-end">
            <Button variant="outline" onClick={clearFilters} size="sm">
              <X className="mr-2 h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading Charts List */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading charts...</span>
        </div>
      ) : (
        <>
          {/* Results Summary */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {loadingCharts.length} of {pagination.totalItems} loading charts
            </p>
          </div>

          {/* Charts Grid */}
          <div className="grid gap-4">
            {loadingCharts.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold">No loading charts found</h3>
                    <p className="text-muted-foreground">
                      Try adjusting your search criteria or create a new loading chart.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              Array.isArray(loadingCharts) && loadingCharts.map((chart) => (
                <Card
                  key={chart.chart_id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleChartClick(chart)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold">{chart.chart_number}</h3>
                          <Badge
                            className={`${statusColors[chart.status as keyof typeof statusColors]} text-white`}
                          >
                            {statusLabels[chart.status as keyof typeof statusLabels]}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Truck className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {chart.vehicle?.registration_number || 'N/A'}
                              {chart.vehicle?.vehicle_type && ` (${chart.vehicle.vehicle_type})`}
                            </span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {chart.destination_branch?.name || 'N/A'}
                              {chart.destination_branch?.code && ` (${chart.destination_branch.code})`}
                            </span>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{format(new Date(chart.created_at), "MMM dd, yyyy HH:mm")}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Package className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {chart.loaded_parcels || 0} / {chart.total_parcels || 0} parcels
                            </span>
                          </div>

                          {chart.loading_type && (
                            <Badge variant="outline">
                              {chart.loading_type}
                            </Badge>
                          )}

                          {chart.memo?.memo_number && (
                            <span className="text-muted-foreground">
                              Memo: {chart.memo.memo_number}
                            </span>
                          )}
                        </div>

                        {chart.notes && (
                          <p className="text-sm text-muted-foreground mt-2">
                            {chart.notes}
                          </p>
                        )}
                      </div>

                      <ChevronRight className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      className={pagination.currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={page === pagination.currentPage}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      className={pagination.currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}

      {/* Loading Chart Details Dialog */}
      <LoadingChartDetailsDialog
        open={isDetailsOpen}
        onOpenChange={setIsDetailsOpen}
        chart={selectedChart}
      />
    </div>
  )
}
