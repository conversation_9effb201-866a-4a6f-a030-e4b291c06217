import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/debug-specific?lr_number=TBT-20250530-0903
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number") || "TBT-20250530-0903";

    console.log("🔍 DEBUG SPECIFIC PARCEL:", lr_number);
    console.log("🔍 DEBUG: User branch ID:", userData.branch_id);

    // 1. Check if parcel exists in parcels table
    const { data: parcel, error: parcelError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        recipient_phone,
        delivery_branch_id,
        number_of_items,
        current_status,
        booking_datetime,
        weight,
        total_amount,
        payment_mode,
        sender_branch:branches!parcels_sender_branch_id_fkey(name, code),
        delivery_branch:branches!parcels_delivery_branch_id_fkey(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    console.log("📦 Parcel data:", parcel);
    console.log("❌ Parcel error:", parcelError);

    if (parcelError || !parcel) {
      return NextResponse.json({
        debug: "Parcel not found",
        lr_number,
        user_branch_id: userData.branch_id,
        error: parcelError
      });
    }

    // 2. Check delivery eligibility criteria
    const isAtDestination = parcel.delivery_branch_id === userData.branch_id;
    const isReceived = parcel.current_status === "Received";

    console.log("🎯 Delivery eligibility check:");
    console.log("  - Parcel delivery_branch_id:", parcel.delivery_branch_id);
    console.log("  - User branch_id:", userData.branch_id);
    console.log("  - Is at destination:", isAtDestination);
    console.log("  - Current status:", parcel.current_status);
    console.log("  - Is received:", isReceived);

    // 3. Check if parcel exists in delivery_eligible_parcels view
    const { data: viewParcel, error: viewError } = await supabase
      .from("delivery_eligible_parcels")
      .select("*")
      .eq("lr_number", lr_number)
      .single();

    console.log("👁️ View data:", viewParcel);
    console.log("❌ View error:", viewError);

    // 4. Check loading chart items for received quantities
    const { data: loadingItems, error: loadingError } = await supabase
      .from("loading_chart_items")
      .select("*")
      .eq("lr_number", lr_number);

    console.log("📋 Loading chart items:", loadingItems);
    console.log("❌ Loading error:", loadingError);

    // 5. Calculate received quantities
    const totalReceived = loadingItems?.reduce((sum, item) => {
      return item.status === 'Received' ? sum + (item.quantity || 0) : sum;
    }, 0) || 0;

    console.log("📊 Total received items:", totalReceived);
    console.log("📊 Total parcel items:", parcel.number_of_items);

    // 6. Check all branches to understand the setup
    const { data: allBranches, error: branchesError } = await supabase
      .from("branches")
      .select("branch_id, name, code");

    console.log("🏢 All branches:", allBranches);

    return NextResponse.json({
      debug: "Detailed parcel analysis",
      lr_number,
      user_branch_id: userData.branch_id,
      parcel_data: parcel,
      delivery_eligibility: {
        is_at_destination: isAtDestination,
        is_received: isReceived,
        should_be_deliverable: isAtDestination && isReceived
      },
      view_data: viewParcel,
      view_error: viewError,
      loading_items: loadingItems,
      received_quantities: {
        total_received: totalReceived,
        total_items: parcel.number_of_items,
        all_received: totalReceived >= parcel.number_of_items
      },
      all_branches: allBranches,
      recommendations: {
        appears_in_view: !!viewParcel,
        meets_basic_criteria: isAtDestination && isReceived,
        has_received_items: totalReceived > 0,
        ready_for_delivery: isAtDestination && isReceived && totalReceived >= parcel.number_of_items
      }
    });

  } catch (error: any) {
    console.error("❌ Error in debug endpoint:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error.message 
    }, { status: 500 });
  }
}
