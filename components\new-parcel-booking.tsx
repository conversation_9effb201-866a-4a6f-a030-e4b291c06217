"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Check, Truck, Package, MapPin, CreditCard, User, Plus, Minus, AlertCircle, FileText, Loader2, RefreshCw } from "lucide-react"
import { MinimumRateChart } from "@/components/minimum-rate-chart"
import { PaymentCheckingDialog } from "@/components/payment-checking-dialog"
import { BookingConfirmationDialog } from "@/components/booking-confirmation-dialog"
import { BookingSuccessPage } from "@/components/booking-success-page"
import { ImageUpload } from "@/components/ui/image-upload"
import { PriceBreakdown } from "@/components/ui/price-breakdown"
import { useRouter, usePathname } from "next/navigation"
import { ParcelType, Branch } from "@/lib/db-helpers"
import { useSupabase } from "@/contexts/supabase-provider"
import { getUserBranchIdAsync } from "@/lib/branch-utils"
//just update
const steps = [
  {
    id: "sender",
    title: "From Party",
    icon: User,
  },
  {
    id: "recipient",
    title: "To Party",
    icon: MapPin,
  },
  {
    id: "parcel",
    title: "Parcel Details",
    icon: Package,
  },
  {
    id: "delivery",
    title: "Delivery Options",
    icon: Truck,
  },
  {
    id: "pricing",
    title: "Price Review",
    icon: CreditCard,
  },
  {
    id: "summary",
    title: "Summary",
    icon: Check,
  },
]

// These will be replaced with data from the API
const cities = [
  { value: "chennai", label: "Chennai" },
  { value: "coimbatore", label: "Coimbatore" },
  { value: "madurai", label: "Madurai" },
  { value: "salem", label: "Salem" },
  { value: "trichy", label: "Trichy" }
]

// This will be replaced with data from the API
const defaultBranches = {
  chennai: [
    { value: "1", label: "Chennai Central" }
  ],
  coimbatore: [
    { value: "2", label: "Coimbatore Main" }
  ],
  madurai: [
    { value: "3", label: "Madurai Central" }
  ],
  salem: [
    { value: "4", label: "Salem Junction" }
  ],
  trichy: [
    { value: "5", label: "Trichy Central" }
  ]
}

// We'll fetch parcel types from the database
const defaultItemTypes = [
  { value: "other", label: "Other" }
]

const deliveryTypes = [
  { value: "standard", label: "Standard Delivery", description: "3-5 business days", rate: 100 },
]

export function NewParcelBooking() {
  const [currentStep, setCurrentStep] = useState(0)
  const { toast } = useToast()
  const router = useRouter()
  const pathname = usePathname()
  const { supabase, session } = useSupabase()

  // Check if we're in admin section
  const isAdminSection = pathname.startsWith('/admin')
  const [isPaymentCheckingOpen, setIsPaymentCheckingOpen] = useState(false)
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false)
  const [showSuccessPage, setShowSuccessPage] = useState(false)
  const [bookingId, setBookingId] = useState("")
  const [parcelTypes, setParcelTypes] = useState<ParcelType[]>([])
  const [branchList, setBranchList] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)
  const [sessionRefreshing, setSessionRefreshing] = useState(false)
  const [userBranchId, setUserBranchId] = useState<number | null>(null)
  const [calculatedTotal, setCalculatedTotal] = useState(0)
  const [pendingParcelData, setPendingParcelData] = useState<any>(null)

  // Refs for keyboard navigation
  const senderNameRef = useRef<HTMLInputElement>(null)
  const senderPhoneRef = useRef<HTMLInputElement>(null)
  const senderAddressRef = useRef<HTMLTextAreaElement>(null)
  const recipientNameRef = useRef<HTMLInputElement>(null)
  const recipientPhoneRef = useRef<HTMLInputElement>(null)
  const recipientAddressRef = useRef<HTMLTextAreaElement>(null)
  const totalWeightRef = useRef<HTMLInputElement>(null)
  const declaredValueRef = useRef<HTMLInputElement>(null)
  const eWayBillRef = useRef<HTMLInputElement>(null)
  const manualRateRef = useRef<HTMLInputElement>(null)
  const submitButtonRef = useRef<HTMLButtonElement>(null)

  // Organize branches by city
  const [branchesByCity, setBranchesByCity] = useState<Record<string, {value: string, label: string}[]>>(defaultBranches)
  const [availableCities, setAvailableCities] = useState<{value: string, label: string}[]>(cities)
  const [formData, setFormData] = useState({
    // Sender Info
    senderName: "",
    senderPhone: "",
    senderAddress: "",

    // Recipient Info
    recipientName: "",
    recipientPhone: "",
    recipientAddress: "",
    recipientCity: "",
    recipientBranch: "none",

    // Parcel Details
    items: [
      { id: 1, type: "other", count: 1, weight: "" }
    ],
    totalWeight: "",
    weightOption: "total", // Always use total weight
    value: "",
    eWayBillNo: "", // Add this new field
    parcelImageUrl: "", // Add image URL field

    // Delivery Options
    paymentType: "paid",
    deliveryType: "standard",
    loadingCharges: 0,
    vehicleCharges: 0,

    // Pricing
    suggestedPrice: 0,
    finalPrice: 0
  })

  const [totalCharges, setTotalCharges] = useState(0)

  // Keyboard navigation functions
  const getFieldSequence = () => {
    const sequence = []

    // Step 0: Sender Info
    if (currentStep === 0) {
      sequence.push(senderNameRef, senderPhoneRef, senderAddressRef)
    }
    // Step 1: Recipient Info
    else if (currentStep === 1) {
      sequence.push(recipientNameRef, recipientPhoneRef, recipientAddressRef)
    }
    // Step 2: Parcel Details
    else if (currentStep === 2) {
      sequence.push(totalWeightRef, declaredValueRef, eWayBillRef)
    }
    // Step 4: Price Review
    else if (currentStep === 4) {
      sequence.push(manualRateRef)
    }

    return sequence.filter(ref => ref.current !== null)
  }

  const focusNextField = (currentRef: React.RefObject<HTMLInputElement | HTMLTextAreaElement>) => {
    const sequence = getFieldSequence()
    const currentIndex = sequence.findIndex(ref => ref === currentRef)

    if (currentIndex !== -1 && currentIndex < sequence.length - 1) {
      // Focus next field in sequence
      const nextRef = sequence[currentIndex + 1]
      if (nextRef.current) {
        nextRef.current.focus()
      }
    } else if (currentIndex === sequence.length - 1) {
      // Last field in current step
      if (currentStep < 4) {
        // Move to next step
        handleNext()
      } else {
        // Focus submit button on final step
        if (submitButtonRef.current) {
          submitButtonRef.current.focus()
        }
      }
    }
  }

  const handleKeyNavigation = (e: React.KeyboardEvent, currentRef: React.RefObject<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Tab' || e.key === 'Enter') {
      e.preventDefault()
      focusNextField(currentRef)
    }
  }

  // Function to calculate charges based on form data
  const calculateCharges = () => {
    let total = 0;

    // Base price calculation based on item types and weight
    let basePrice = 0;

    // Calculate base price based on items
    formData.items.forEach(item => {
      // Find the parcel type in the fetched parcel types
      const parcelType = parcelTypes.find(type => type.type_id.toString() === item.type);

      // Use the base_rate from the parcel type, or default to 100
      const typeBasePrice = parcelType ? parcelType.base_rate : 100;

      basePrice += typeBasePrice * item.count;

      if (formData.weightOption === 'per_item' && item.weight) {
        basePrice += parseFloat(item.weight) * 10 * item.count;
      }
    });

    if (formData.weightOption === 'total' && formData.totalWeight) {
      basePrice += parseFloat(formData.totalWeight) * 15;
    }

    if (formData.value) {
      basePrice += parseFloat(formData.value) * 0.005;
    }

    const selectedDelivery = deliveryTypes.find(t => t.value === formData.deliveryType);
    if (selectedDelivery) {
      basePrice += selectedDelivery.rate;
    }

    // Remove the direct addition of loading and vehicle charges
    // They will be calculated as percentages in the final price review

    return Math.round(basePrice);
  }

  // Function to extract city from address
  const extractCityFromAddress = (address: string | null): string => {
    if (!address) return '';

    // Try to extract city from address that contains a dash with city and pincode
    // Example: "45, Anna Salai, Chennai - 600002"
    const cityPincodeMatch = address.match(/([A-Za-z\s]+)\s*-\s*\d+/);
    if (cityPincodeMatch && cityPincodeMatch[1]) {
      return cityPincodeMatch[1].trim();
    }

    // If no match, try to extract any word that might be a city
    // This is a simple approach - in a real app, you'd use a more sophisticated method
    const words = address.split(/[\s,]+/);
    for (const city of ['Chennai', 'Coimbatore', 'Madurai', 'Salem', 'Trichy']) {
      if (words.includes(city)) {
        return city;
      }
    }

    return '';
  };

  // Check authentication status and permissions
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession) {
        console.log('No active session detected, but continuing without redirect');
      } else {
        console.log('Active session detected:', currentSession.user.email);

        // Check if user has permissions to create parcels
        try {
          // Try to get user's role from the users table
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('role, branch_id')
            .eq('email', currentSession.user.email)
            .single();

          if (userError) {
            console.error('Error fetching user role:', userError);
          } else if (userData) {
            console.log('User role:', userData.role);
            console.log('User branch ID:', userData.branch_id);

            // Set the user's branch ID
            if (userData.branch_id) {
              setUserBranchId(userData.branch_id);
            } else {
              // If no branch ID in the direct query, try the API
              try {
                const branchId = await getUserBranchIdAsync();
                if (branchId !== null) {
                  console.log('User branch ID from API:', branchId);
                  setUserBranchId(branchId);
                } else {
                  console.warn('No branch ID found for user');
                }
              } catch (apiError: any) {
                console.error('Error fetching branch ID from API:', apiError);
              }
            }
          }
        } catch (error: any) {
          console.error('Error checking user permissions:', error);
        }
      }
    };

    checkAuth();
  }, [supabase]);

  // Fetch parcel types, cities, and branches from the database
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch parcel types (authentication is handled via session cookie)
        const parcelTypesResponse = await fetch('/api/parceltypes');
        if (!parcelTypesResponse.ok) {
          throw new Error('Failed to fetch parcel types');
        }
        const parcelTypesData = await parcelTypesResponse.json();
        setParcelTypes(parcelTypesData);

        // Try to fetch cities
        let citiesData: any[] = [];
        let useCitiesTable = false;

        try {
          console.log('Fetching cities from API...');
          const citiesResponse = await fetch('/api/cities');
          if (citiesResponse.ok) {
            const responseData = await citiesResponse.json();
            console.log('Cities API response:', responseData);
            citiesData = responseData.cities || [];
            console.log('Extracted cities data:', citiesData);
            useCitiesTable = citiesData.length > 0;
          } else {
            console.error('Cities API failed with status:', citiesResponse.status);
          }
        } catch (error: any) {
          console.warn('Cities table might not exist yet, falling back to address extraction:', error);
        }

        // Fetch branches
        console.log('Fetching branches from API...');
        const branchesResponse = await fetch('/api/branches');
        if (!branchesResponse.ok) {
          throw new Error('Failed to fetch branches');
        }
        const branchesResponseData = await branchesResponse.json();
        console.log('Branches API response:', branchesResponseData);
        const branchesData = branchesResponseData.branches || branchesResponseData;
        console.log('Extracted branches data:', branchesData);
        setBranchList(branchesData);

        // Organize branches by city
        if (branchesData && branchesData.length > 0) {
          if (useCitiesTable && citiesData.length > 0) {
            console.log('Using cities table for organization');
            // Use cities table
            // Create city options
            const cityOptions = citiesData.map((city: any) => ({
              value: city.city_id.toString(),
              label: city.name
            }));
            console.log('Created city options:', cityOptions);

            setAvailableCities(cityOptions.length > 0 ? cityOptions : []);

            // Group branches by city
            const branchesMap: Record<string, {value: string, label: string}[]> = {};

            citiesData.forEach((city: any) => {
              const cityId = city.city_id.toString();
              const cityBranches = branchesData
                .filter((branch: any) => branch.city_id === city.city_id)
                .map((branch: any) => ({
                  value: branch.branch_id.toString(),
                  label: branch.name
                }));

              console.log(`City ${city.name} (ID: ${cityId}) has ${cityBranches.length} branches:`, cityBranches);

              if (cityBranches.length > 0) {
                branchesMap[cityId] = cityBranches;
              }
            });

            console.log('Final branches map:', branchesMap);

            // If we have branches by city, update the state
            if (Object.keys(branchesMap).length > 0) {
              setBranchesByCity(branchesMap);
              console.log('Updated branchesByCity state');
            } else {
              console.warn('No branches found for any city');
            }
          } else {
            console.log('Falling back to address extraction method');
            // Fall back to extracting city from address
            // Extract cities from branch addresses
            const branchCities = branchesData.map(branch => extractCityFromAddress(branch.address));
            const uniqueCities = [...new Set(branchCities.filter(city => city))];
            console.log('Extracted cities from addresses:', uniqueCities);

            // Create city options
            const cityOptions = uniqueCities.map(city => ({
              value: city.toLowerCase().replace(/\s+/g, '_'),
              label: city
            }));
            console.log('Created fallback city options:', cityOptions);

            setAvailableCities(cityOptions.length > 0 ? cityOptions : []);

            // Group branches by city
            const branchesMap: Record<string, {value: string, label: string}[]> = {};

            uniqueCities.forEach(city => {
              if (!city) return;

              const cityKey = city.toLowerCase().replace(/\s+/g, '_');
              const cityBranches = branchesData
                .filter(branch => extractCityFromAddress(branch.address) === city)
                .map(branch => ({
                  value: branch.branch_id.toString(),
                  label: branch.name
                }));

              if (cityBranches.length > 0) {
                branchesMap[cityKey] = cityBranches;
              }
            });

            // If we have branches by city, update the state
            if (Object.keys(branchesMap).length > 0) {
              setBranchesByCity(branchesMap);
            }
          }
        }
      } catch (error: any) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load necessary data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast, supabase]);

  // Calculate charges and update state when form data changes
  useEffect(() => {
    const total = calculateCharges();
    setTotalCharges(total);
    // Only update pricing if it's different to avoid infinite loops
    if (total !== formData.suggestedPrice) {
      setFormData(prev => ({
        ...prev,
        suggestedPrice: total,
        finalPrice: calculatedTotal > 0 ? calculatedTotal : Math.round(total * 1.43) // Use calculated total from PriceBreakdown
      }));
    }
  }, [
    formData.items,
    formData.weightOption,
    formData.totalWeight,
    formData.value,
    formData.deliveryType,
    formData.suggestedPrice // Add this to prevent infinite loops
  ]);

  // Auto-focus first field when step changes
  useEffect(() => {
    const timer = setTimeout(() => {
      const sequence = getFieldSequence()
      if (sequence.length > 0 && sequence[0].current) {
        sequence[0].current.focus()
      }
    }, 100) // Small delay to ensure DOM is ready

    return () => clearTimeout(timer)
  }, [currentStep])

  const validatePhoneNumber = (phone: string): string => {
    // Remove any non-digit characters
    const digitsOnly = phone.replace(/\D/g, '');

    // Ensure it's exactly 10 digits
    if (digitsOnly.length > 10) {
      return digitsOnly.slice(0, 10);
    }

    return digitsOnly;
  };

  const handleInputChange = (field: string, value: string | number) => {
    // Apply phone validation for phone fields
    if (field === 'senderPhone' || field === 'recipientPhone') {
      value = validatePhoneNumber(value.toString());
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }

  const handleNext = () => {
    // Validate phone numbers based on current step
    if (currentStep === 0 && formData.senderPhone && formData.senderPhone.length !== 10) {
      toast({
        title: "Invalid Phone Number",
        description: "Sender phone number must be exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    if (currentStep === 1 && formData.recipientPhone && formData.recipientPhone.length !== 10) {
      toast({
        title: "Invalid Phone Number",
        description: "Recipient phone number must be exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    // Check if E-Way bill is required but not provided (₹45,000 threshold)
    if (parseFloat(formData.value) >= 45000 && !formData.eWayBillNo) {
      toast({
        title: "E-Way Bill Required",
        description: "Please enter the E-Way bill number for items valued ₹45,000 or above",
        variant: "destructive",
      });
      return;
    }

    // Check if parcel image is required but not provided (₹45,000 threshold)
    if (parseFloat(formData.value) >= 45000 && !formData.parcelImageUrl) {
      toast({
        title: "Parcel Image Required",
        description: "Please upload a parcel image for items valued ₹45,000 or above",
        variant: "destructive",
      });
      return;
    }

    // Validate price on the Price Review step
    if (currentStep === 4 && (!formData.finalPrice || formData.finalPrice < 1)) {
      toast({
        title: "Invalid Price",
        description: "Please enter a valid rate amount",
        variant: "destructive",
      });
      return;
    }

    setCurrentStep(currentStep + 1);
  }

  // Generate LR number based on pattern
  const generateLRNumber = () => {
    // Format: BRANCH_CODE-YYYYMMDD-XXXX
    const today = new Date();
    const dateStr = today.getFullYear().toString() +
      (today.getMonth() + 1).toString().padStart(2, '0') +
      today.getDate().toString().padStart(2, '0');

    // Get branch code from user's branch if available
    let branchCode = 'KPN'; // Default fallback

    if (userBranchId && branchList.length > 0) {
      // Find the user's branch in the branch list
      const userBranch = branchList.find(branch => branch.branch_id === userBranchId);
      if (userBranch && userBranch.code) {
        branchCode = userBranch.code;
      }
    } else if (branchList.length > 0) {
      // Fallback to first branch if user branch not found
      branchCode = branchList[0].code || 'KPN';
    }

    // Random 4-digit number
    const randomId = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    return `${branchCode}-${dateStr}-${randomId}`;
  };

  // Reference to minimum rate chart
  // This is just for UI reference - not for automatic validation
  const getMinimumRateReference = () => {
    // In a real implementation, this might look up values from a rate chart
    // based on weight, distance, etc.
    return {
      minRate: 100 // Just a reference value
    };
  };

  const handleSubmit = async () => {
    // Validate phone numbers before submitting
    if (formData.senderPhone.length !== 10) {
      toast({
        title: "Invalid Sender Phone",
        description: "Sender phone number must be exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    if (formData.recipientPhone.length !== 10) {
      toast({
        title: "Invalid Recipient Phone",
        description: "Recipient phone number must be exactly 10 digits",
        variant: "destructive",
      });
      return;
    }

    // We don't automatically validate against minimum rate
    // The user is responsible for referring to the rate chart
    // and entering an appropriate rate

    // Generate LR number using the pattern
    const lrNumber = generateLRNumber();
    setBookingId(lrNumber);

    try {
      // Use the user's branch ID for the sender branch if available
      let senderBranchId: number;

      if (userBranchId) {
        // Use the user's branch ID
        senderBranchId = userBranchId;
        console.log('Using user branch ID for sender:', senderBranchId);
      } else {
        // Try to get from localStorage as fallback
        const localBranchId = localStorage.getItem('user_branch_id');
        if (localBranchId) {
          senderBranchId = parseInt(localBranchId);
          console.log('Using branch ID from localStorage:', senderBranchId);
          // Update state for future use
          setUserBranchId(senderBranchId);
        } else {
          // Fallback to first branch if no branch ID is available anywhere
          senderBranchId = branchList.length > 0 ? branchList[0].branch_id : 1;
          console.log('Fallback to first branch for sender:', senderBranchId);
        }
      }

      // Get the recipient branch ID from the form
      const recipientBranchId = formData.recipientBranch && formData.recipientBranch !== "none" ? parseInt(formData.recipientBranch) :
        (branchList.length > 1 ? branchList[1].branch_id : 1);

      const parcelData = {
        lr_number: lrNumber, // Using lr_number as expected by the API
        sender_name: formData.senderName,
        sender_phone: formData.senderPhone,
        sender_branch_id: senderBranchId,
        recipient_name: formData.recipientName,
        recipient_phone: formData.recipientPhone,
        delivery_branch_id: recipientBranchId,
        number_of_items: formData.items.reduce((total, item) => total + item.count, 0),
        item_type: formData.items[0]?.type ? parseInt(formData.items[0].type) : null,
        weight: formData.weightOption === 'total'
          ? parseFloat(formData.totalWeight)
          : formData.items.reduce((total, item) => total + (parseFloat(item.weight || '0') * item.count), 0),
        payment_mode: formData.paymentType === 'paid' ? 'Paid' : 'To Pay',
        delivery_charges: calculatedTotal > 0 ? calculatedTotal : parseFloat(formData.finalPrice.toString()),
        base_price: parseFloat(formData.suggestedPrice.toString()),
        tax_details: JSON.stringify({
          gst: Math.round(formData.suggestedPrice * 0.18),
          loading_charges: Math.round(formData.suggestedPrice * 0.06),
          vehicle_charges: Math.round(formData.suggestedPrice * 0.15)
        }),
        total_amount: calculatedTotal > 0 ? calculatedTotal : parseFloat(formData.finalPrice.toString()),
        current_status: 'Booked',
        expected_delivery_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 days from now
        // Store e-way bill number if provided (mandatory for values >= 45,000)
        eway_bill_number: formData.eWayBillNo || null,
        declared_value: formData.value ? parseFloat(formData.value) : null, // Add the declared value
        delivery_type: formData.deliveryType, // Add the delivery type
        parcel_img_url: formData.parcelImageUrl || null // Add the image URL
      };

      // Store parcel data for later creation after payment
      setPendingParcelData(parcelData);

      // Follow different paths based on payment type (PAID vs TO PAY)
      if (formData.paymentType === "paid") {
        // For PAID LR, redirect to payment flow
        setIsPaymentCheckingOpen(true);
      } else {
        // For TO PAY LR, create parcel directly and show success page
        const success = await createParcelFromPendingData(parcelData);
        if (success) {
          console.log('Opening success page for cash payment with formData:', formData);
          console.log('Opening success page with parcelTypes:', parcelTypes);
          setShowSuccessPage(true);
        }
      }
    } catch (error: any) {
      console.error('Error creating parcel:', error);

      // Check if it's a Supabase error with more details
      if (error && typeof error === 'object' && 'code' in error) {
        const supabaseError = error as { code: string; message: string; details?: string };
        console.error('Supabase error details:', supabaseError);

        toast({
          title: 'Database Error',
          description: `${supabaseError.message || 'Unknown error'} (Code: ${supabaseError.code})`,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to create parcel. Please try again.',
          variant: 'destructive',
        });
      }
    }
  }

  const createParcelFromPendingData = async (parcelData?: any) => {
    const dataToUse = parcelData || pendingParcelData;
    if (!dataToUse) {
      console.error('No parcel data to create');
      return false;
    }

    try {
      // Log session status but don't block the request
      if (!session) {
        console.log('Warning: No session detected, but attempting to create parcel anyway');
      }

      // Try to create the parcel directly using Supabase client first
      console.log('Attempting to create parcel directly with Supabase client');
      try {
        const { data: directData, error: directError } = await supabase
          .from('parcels')
          .insert(dataToUse)
          .select()
          .single();

        if (directError) {
          console.error('Error creating parcel directly:', directError);
          // Fall back to API route if direct creation fails
          console.log('Falling back to API route');
        } else {
          console.log('Parcel created successfully directly:', directData);
          // Clear pending data after successful creation
          setPendingParcelData(null);
          return true;
        }
      } catch (directError: any) {
        console.error('Unexpected error creating parcel directly:', directError);
        // Continue to API route as fallback
      }

      // Fallback: Save parcel to database via API route
      console.log('Using API route to create parcel');
      const response = await fetch('/api/parcels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToUse),
      });

      if (!response.ok) {
        // Try to get detailed error message
        let errorMessage = 'Failed to create parcel';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = errorData.error;

            // If unauthorized, just show the error without trying to refresh
            if (errorMessage === 'Unauthorized' || response.status === 401) {
              console.error('Authentication error when creating parcel:', errorMessage);

              // Don't try to auto-refresh, just show the error
              toast({
                title: 'Authentication Error',
                description: 'There was an authentication error. Please try using the "Refresh Session" button and try again.',
                variant: 'destructive',
              });
              return false;
            }
          }
        } catch (e: any) {
          // If we can't parse the error, use the default message
        }
        throw new Error(errorMessage);
      }

      // Clear pending data after successful creation
      setPendingParcelData(null);
      return true;
    } catch (error: any) {
      console.error('Error creating parcel:', error);

      // Check if it's a Supabase error with more details
      if (error && typeof error === 'object' && 'code' in error) {
        const supabaseError = error as { code: string; message: string; details?: string };
        console.error('Supabase error details:', supabaseError);

        toast({
          title: 'Database Error',
          description: `${supabaseError.message || 'Unknown error'} (Code: ${supabaseError.code})`,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to create parcel. Please try again.',
          variant: 'destructive',
        });
      }
      return false;
    }
  }

  const handlePaymentSuccess = async () => {
    // Create parcel after successful payment
    const parcelCreated = await createParcelFromPendingData();
    if (parcelCreated) {
      // Skip PaymentSuccessDialog and go directly to confirmation dialog
      console.log('Opening confirmation dialog directly after payment success with formData:', formData);
      console.log('Opening confirmation dialog with parcelTypes:', parcelTypes);
      setIsConfirmationDialogOpen(true);
    }
  }

  const handleGenerateReceipt = () => {
    toast({
      title: "Receipt Generated",
      description: "Receipt has been generated and is ready for printing.",
    })
  }

  // Function to manually refresh the session
  const refreshSession = async () => {
    setSessionRefreshing(true);
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Error refreshing session:', error);
        toast({
          title: 'Session Refresh Failed',
          description: 'Unable to refresh your session. You may need to log out and log back in.',
          variant: 'destructive',
        });
      } else if (data.session) {
        console.log('Session refreshed successfully');
        toast({
          title: 'Session Refreshed',
          description: 'Your session has been refreshed successfully.',
        });
      } else {
        console.warn('No session returned after refresh');
        toast({
          title: 'Session Issue',
          description: 'No active session found. You may need to log out and log back in.',
          variant: 'destructive',
        });
      }
    } catch (error: any) {
      console.error('Unexpected error refreshing session:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while refreshing your session.',
        variant: 'destructive',
      });
    } finally {
      setSessionRefreshing(false);
    }
  }

  const handleBookingComplete = () => {
    // Close the confirmation dialog
    setIsConfirmationDialogOpen(false);

    // Reset the form
    setCurrentStep(0)
    setFormData({
      // Sender Info
      senderName: "",
      senderPhone: "",
      senderAddress: "",

      // Recipient Info
      recipientName: "",
      recipientPhone: "",
      recipientAddress: "",
      recipientCity: "",
      recipientBranch: "none",

      // Parcel Details
      items: [
        { id: 1, type: "other", count: 1, weight: "" }
      ],
      totalWeight: "",
      weightOption: "total",
      value: "",
      eWayBillNo: "",
      parcelImageUrl: "",

      // Delivery Options
      paymentType: "paid",
      deliveryType: "standard",
      loadingCharges: 0,
      vehicleCharges: 0,

      // Pricing
      suggestedPrice: 0,
      finalPrice: 0
    })

    // Show success message
    toast({
      title: "Booking Completed",
      description: "Your booking has been completed successfully.",
    })
  }

  const handleSuccessPageNewBooking = () => {
    // Close the success page
    setShowSuccessPage(false);

    // Reset the form
    setCurrentStep(0)
    setFormData({
      // Sender Info
      senderName: "",
      senderPhone: "",
      senderAddress: "",
      recipientName: "",
      recipientPhone: "",
      recipientAddress: "",
      recipientCity: "",
      recipientBranch: "none",

      // Parcel Details
      items: [
        { id: 1, type: "other", count: 1, weight: "" }
      ],
      totalWeight: "",
      weightOption: "total",
      value: "",
      eWayBillNo: "",
      parcelImageUrl: "",

      // Delivery Options
      paymentType: "paid",
      deliveryType: "standard",
      loadingCharges: 0,
      vehicleCharges: 0,

      // Pricing
      suggestedPrice: 0,
      finalPrice: 0,
    })

    // Reset booking ID
    setBookingId("")
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Sender Info
        return (
          <div className="space-y-4">
            <div className="grid gap-4">
              <div>
                <Input
                  ref={senderNameRef}
                  placeholder="Sender Name"
                  value={formData.senderName}
                  onChange={(e) => handleInputChange("senderName", e.target.value)}
                  onKeyDown={(e) => handleKeyNavigation(e, senderNameRef)}
                />
              </div>
              <div>
                <Input
                  ref={senderPhoneRef}
                  placeholder="Phone Number (10 digits)"
                  value={formData.senderPhone}
                  onChange={(e) => handleInputChange("senderPhone", e.target.value)}
                  onKeyDown={(e) => handleKeyNavigation(e, senderPhoneRef)}
                  maxLength={10}
                />
                {formData.senderPhone && formData.senderPhone.length !== 10 && (
                  <p className="text-xs text-red-500 mt-1">Phone number must be 10 digits</p>
                )}
              </div>
            </div>
            <div>
              <Textarea
                ref={senderAddressRef}
                placeholder="Complete Address"
                value={formData.senderAddress}
                onChange={(e) => handleInputChange("senderAddress", e.target.value)}
                onKeyDown={(e) => handleKeyNavigation(e, senderAddressRef)}
                className="min-h-[100px]"
              />
            </div>

          </div>
        )

      case 1: // Recipient Info
        return (
          <div className="space-y-4">
            <div className="grid gap-4">
              <div>
                <Input
                  ref={recipientNameRef}
                  placeholder="Recipient Name"
                  value={formData.recipientName}
                  onChange={(e) => handleInputChange("recipientName", e.target.value)}
                  onKeyDown={(e) => handleKeyNavigation(e, recipientNameRef)}
                />
              </div>
              <div>
                <Input
                  ref={recipientPhoneRef}
                  placeholder="Phone Number (10 digits)"
                  value={formData.recipientPhone}
                  onChange={(e) => handleInputChange("recipientPhone", e.target.value)}
                  onKeyDown={(e) => handleKeyNavigation(e, recipientPhoneRef)}
                  maxLength={10}
                />
                {formData.recipientPhone && formData.recipientPhone.length !== 10 && (
                  <p className="text-xs text-red-500 mt-1">Phone number must be 10 digits</p>
                )}
              </div>
            </div>
            <div>
              <Textarea
                ref={recipientAddressRef}
                placeholder="Complete Address"
                value={formData.recipientAddress}
                onChange={(e) => handleInputChange("recipientAddress", e.target.value)}
                onKeyDown={(e) => handleKeyNavigation(e, recipientAddressRef)}
                className="min-h-[100px]"
              />
            </div>
            <div className="grid gap-4">
              <Select
                value={formData.recipientCity}
                onValueChange={(value) => {
                  handleInputChange("recipientCity", value)
                  handleInputChange("recipientBranch", "none") // Reset branch when city changes
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select City" />
                </SelectTrigger>
                <SelectContent>
                  {loading ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : availableCities.length > 0 ? (
                    availableCities.map((city) => (
                      <SelectItem key={city.value} value={city.value}>
                        {city.label}
                      </SelectItem>
                    ))
                  ) : (
                    cities.map((city) => (
                      <SelectItem key={city.value} value={city.value}>
                        {city.label}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>

              <Select
                value={formData.recipientBranch}
                onValueChange={(value) => handleInputChange("recipientBranch", value)}
                disabled={!formData.recipientCity}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Branch" />
                </SelectTrigger>
                <SelectContent>
                  {loading ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  ) : formData.recipientCity && branchesByCity &&
                    Object.keys(branchesByCity).includes(formData.recipientCity) ? (
                    branchesByCity[formData.recipientCity].map((branch) => (
                      <SelectItem key={branch.value} value={branch.value}>
                        {branch.label}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>No branches available</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

          </div>
        )

      case 2: // Parcel Details
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Items</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newItems = [...formData.items, {
                      id: formData.items.length + 1,
                      type: "other",
                      count: 1,
                      weight: ""
                    }];
                    setFormData(prev => ({ ...prev, items: newItems }));
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add More Item
                </Button>
              </div>

              {formData.items.map((item, index) => (
                <Card key={item.id} className="p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h5 className="text-sm font-medium">Item {index + 1}</h5>
                      {formData.items.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const newItems = formData.items.filter((_, i) => i !== index);
                            setFormData(prev => ({ ...prev, items: newItems }));
                          }}
                        >
                          <Minus className="h-4 w-4" />
                          Remove
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Select
                          value={item.type}
                          onValueChange={(value) => {
                            const newItems = [...formData.items];
                            newItems[index].type = value;
                            setFormData(prev => ({ ...prev, items: newItems }));
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select Item Type" />
                          </SelectTrigger>
                          <SelectContent>
                            {loading ? (
                              <div className="flex items-center justify-center p-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                              </div>
                            ) : parcelTypes.length > 0 ? (
                              parcelTypes.map((type) => (
                                <SelectItem key={type.type_id.toString()} value={type.type_id.toString()}>
                                  {type.type_name}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="other">Other</SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </div>

                      <Input
                        type="number"
                        placeholder="Quantity"
                        value={item.count === 0 ? "" : item.count}
                        onChange={(e) => {
                          const value = e.target.value;
                          const newItems = [...formData.items];

                          // Allow empty value while typing (set to 0 temporarily)
                          if (value === "") {
                            newItems[index].count = 0;
                          } else {
                            const numValue = parseInt(value);
                            // Only update if it's a valid number
                            if (!isNaN(numValue) && numValue >= 0) {
                              newItems[index].count = numValue;
                            }
                          }

                          setFormData(prev => ({ ...prev, items: newItems }));
                        }}
                        onBlur={(e) => {
                          // Set to 1 if empty or 0 when user leaves the field
                          const value = parseInt(e.target.value);
                          if (isNaN(value) || value < 1) {
                            const newItems = [...formData.items];
                            newItems[index].count = 1;
                            setFormData(prev => ({ ...prev, items: newItems }));
                          }
                        }}
                        min="1"
                      />


                    </div>
                  </div>
                </Card>
              ))}
            </div>

            <Separator />

            <div>
              <h4 className="mb-4 text-sm font-medium">Total Parcel Weight</h4>
              <Input
                ref={totalWeightRef}
                type="number"
                placeholder="Total Weight (kg)"
                value={formData.totalWeight}
                onChange={(e) => handleInputChange("totalWeight", e.target.value)}
                onKeyDown={(e) => handleKeyNavigation(e, totalWeightRef)}
              />
              <p className="text-xs text-muted-foreground mt-1">Enter the total weight of all items in kg</p>
            </div>

            <Separator />

            <div>
              <h4 className="mb-4 text-sm font-medium">Declared Value</h4>
              <Input
                ref={declaredValueRef}
                type="number"
                placeholder="Value (₹)"
                value={formData.value}
                onChange={(e) => handleInputChange("value", e.target.value)}
                onKeyDown={(e) => handleKeyNavigation(e, declaredValueRef)}
              />
              <p className="text-xs text-muted-foreground mt-1">Total value of all items in the parcel</p>
            </div>

            <Separator />

            <div>
              <div className="flex items-center gap-2 mb-4">
                <h4 className="text-sm font-medium">
                  E-Way Bill Number
                  {parseFloat(formData.value) >= 45000 && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </h4>
                {parseFloat(formData.value) >= 45000 ? (
                  <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                    Required
                  </span>
                ) : (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    Optional
                  </span>
                )}
              </div>

              {parseFloat(formData.value) >= 45000 && (
                <div className="flex items-start mb-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-700">
                    E-Way Bill is mandatory for items valued ₹45,000 or above as per GST regulations
                  </p>
                </div>
              )}

              <Input
                ref={eWayBillRef}
                placeholder="Enter E-Way Bill Number"
                value={formData.eWayBillNo}
                onChange={(e) => handleInputChange("eWayBillNo", e.target.value)}
                onKeyDown={(e) => handleKeyNavigation(e, eWayBillRef)}
                className={parseFloat(formData.value) >= 45000 && !formData.eWayBillNo ? "border-red-300 focus:border-red-500" : ""}
              />

              {parseFloat(formData.value) >= 45000 && !formData.eWayBillNo && (
                <p className="text-sm text-red-500 mt-1">
                  Please enter the E-Way bill number to proceed
                </p>
              )}

              <p className="text-xs text-muted-foreground mt-1">
                {parseFloat(formData.value) >= 45000
                  ? "Required for parcels valued ₹45,000 or above"
                  : "Optional for parcels below ₹45,000"
                }
              </p>
            </div>

            <Separator />

            <div>
              <div className="flex items-center gap-2 mb-4">
                <h4 className="text-sm font-medium">
                  Parcel Image
                  {parseFloat(formData.value) >= 45000 && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </h4>
                {parseFloat(formData.value) >= 45000 ? (
                  <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                    Required
                  </span>
                ) : (
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    Optional
                  </span>
                )}
              </div>

              {parseFloat(formData.value) >= 45000 && (
                <div className="flex items-start mb-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                  <AlertCircle className="h-4 w-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-700">
                    Parcel image is mandatory for items valued ₹45,000 or above for verification purposes
                  </p>
                </div>
              )}

              <ImageUpload
                onImageUploaded={(url) => handleInputChange("parcelImageUrl", url)}
                onImageRemoved={() => handleInputChange("parcelImageUrl", "")}
                currentImageUrl={formData.parcelImageUrl}
                maxSizeInMB={5}
                acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
              />

              {parseFloat(formData.value) >= 45000 && !formData.parcelImageUrl && (
                <p className="text-sm text-red-500 mt-2">
                  Please upload a parcel image to proceed
                </p>
              )}

              <p className="text-xs text-muted-foreground mt-2">
                {parseFloat(formData.value) >= 45000
                  ? "Required for parcels valued ₹45,000 or above"
                  : "Upload an image of the parcel for reference (optional)"
                }
              </p>
            </div>
          </div>
        )

      case 3: // Delivery Options
        return (
          <div className="space-y-6">
            <div>
              <h4 className="mb-4 text-sm font-medium">Payment Mode</h4>
              <RadioGroup
                value={formData.paymentType}
                onValueChange={(value) => handleInputChange("paymentType", value)}
                className="grid grid-cols-2 gap-4"
              >
                <div>
                  <RadioGroupItem
                    value="paid"
                    id="paid"
                    className="peer sr-only"
                  />
                  <label
                    htmlFor="paid"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <CreditCard className="mb-2 h-6 w-6" />
                    <p className="text-sm font-medium">Paid</p>
                  </label>
                </div>
                <div>
                  <RadioGroupItem
                    value="to_pay"
                    id="to_pay"
                    className="peer sr-only"
                  />
                  <label
                    htmlFor="to_pay"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <Package className="mb-2 h-6 w-6" />
                    <p className="text-sm font-medium">To Pay</p>
                  </label>
                </div>
              </RadioGroup>
            </div>

            <Separator />

            <div>
              <h4 className="mb-4 text-sm font-medium">Delivery Type</h4>
              <RadioGroup
                value={formData.deliveryType}
                onValueChange={(value) => handleInputChange("deliveryType", value)}
                className="grid gap-4"
              >
                {deliveryTypes.map((type) => (
                  <div key={type.value}>
                    <RadioGroupItem
                      value={type.value}
                      id={type.value}
                      className="peer sr-only"
                    />
                    <label
                      htmlFor={type.value}
                      className="flex items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <div>
                        <p className="font-medium">{type.label}</p>
                        <p className="text-sm text-muted-foreground">{type.description}</p>
                      </div>
                      <p className="font-medium">₹{type.rate}</p>
                    </label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          </div>
        )

      case 4: // Price Review
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">Rate Input</h4>
              <p className="text-sm text-muted-foreground">
                Enter the rate you want to charge for this booking.
              </p>

              {/* Rate Input Section */}
              <Card className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">Enter Rate</h5>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      <span className="mr-1">Refer to minimum rate chart</span>
                      <MinimumRateChart />
                    </div>
                  </div>
                  <Separator />

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="manual-rate" className="text-sm font-medium">
                        Enter Rate Amount (₹)
                      </label>
                      <div className="flex items-center mt-2">
                        <Input
                          ref={manualRateRef}
                          id="manual-rate"
                          type="number"
                          placeholder="Enter rate"
                          value={formData.manualRate || ''}
                          onChange={(e) => {
                            const value = e.target.value;
                            // Update manual rate
                            setFormData(prev => ({
                              ...prev,
                              manualRate: value,
                              // If manual rate is provided, use it as final price (minimum 1)
                              finalPrice: value ? Math.max(1, parseFloat(value)) : 0
                            }));
                          }}
                          onKeyDown={(e) => handleKeyNavigation(e, manualRateRef)}
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          className="ml-2"
                          onClick={() => {
                            // Set to reference rate
                            const minRate = getMinimumRateReference().minRate;
                            setFormData(prev => ({
                              ...prev,
                              manualRate: minRate.toString(),
                              finalPrice: minRate
                            }));
                          }}
                        >
                          Use Reference
                        </Button>
                      </div>

                      {/* Rate reference message */}
                      <p className="text-sm text-muted-foreground mt-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        Please refer to the minimum rate chart before entering a rate.
                      </p>
                    </div>

                    <div className="flex justify-between font-medium">
                      <span>Base Price:</span>
                      <span className="text-lg">₹{formData.finalPrice || 0}</span>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Price Breakdown */}
              {formData.finalPrice > 0 && (
                <PriceBreakdown
                  basePrice={formData.finalPrice}
                  onTotalChange={(total) => setCalculatedTotal(total)}
                  className="mt-4"
                />
              )}
            </div>
          </div>
        )

      case 5: // Summary
        return (
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h4 className="font-semibold">Sender Details</h4>
                <div className="space-y-2">
                  <p><span className="font-medium">Name:</span> {formData.senderName}</p>
                  <p><span className="font-medium">Phone:</span> {formData.senderPhone}</p>
                  <p><span className="font-medium">Address:</span> {formData.senderAddress}</p>
                </div>
              </div>
              <div className="space-y-4">
                <h4 className="font-semibold">Recipient Details</h4>
                <div className="space-y-2">
                  <p><span className="font-medium">Name:</span> {formData.recipientName}</p>
                  <p><span className="font-medium">Phone:</span> {formData.recipientPhone}</p>
                  <p><span className="font-medium">Address:</span> {formData.recipientAddress}</p>
                  {formData.recipientCity && formData.recipientBranch && formData.recipientBranch !== "none" && (
                    <p>
                      <span className="font-medium">Branch:</span>{" "}
                      {branchesByCity && branchesByCity[formData.recipientCity] ?
                        branchesByCity[formData.recipientCity].find(
                          b => b.value === formData.recipientBranch
                        )?.label || "Selected Branch"
                        : "Selected Branch"}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h4 className="font-semibold">Parcel Details</h4>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <p>
                    <span className="font-medium">Items:</span>
                  </p>
                  <ul className="list-disc pl-5 space-y-1">
                    {formData.items.map((item, index) => (
                      <li key={item.id}>
                        {item.count} × {parcelTypes.find(t => t.type_id.toString() === item.type)?.type_name || 'Item'}
                      </li>
                    ))}
                  </ul>

                  {formData.totalWeight && (
                    <p><span className="font-medium">Total Weight:</span> {formData.totalWeight} kg</p>
                  )}
                </div>
                <div className="space-y-2">
                  <p>
                    <span className="font-medium">Delivery Type:</span>{" "}
                    {deliveryTypes.find(t => t.value === formData.deliveryType)?.label}
                  </p>
                  <p>
                    <span className="font-medium">Payment Mode:</span>{" "}
                    {formData.paymentType === "paid" ? "Paid" : "To Pay"}
                  </p>
                  {formData.value && (
                    <p>
                      <span className="font-medium">Declared Value:</span> ₹{formData.value}
                      {formData.eWayBillNo && (
                        <>
                          <br />
                          <span className="font-medium">E-Way Bill No:</span> {formData.eWayBillNo}
                        </>
                      )}
                    </p>
                  )}
                  <p>
                    <span className="font-medium">Final Price:</span>{" "}
                    ₹{formData.finalPrice}
                  </p>
                </div>
              </div>

              {formData.parcelImageUrl && (
                <div className="space-y-2">
                  <h5 className="font-medium">Parcel Image</h5>
                  <div className="w-full max-w-sm">
                    <img
                      src={formData.parcelImageUrl}
                      alt="Parcel"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Price Breakdown in Summary */}
            {formData.finalPrice > 0 && (
              <PriceBreakdown
                basePrice={formData.finalPrice}
                showTitle={true}
                onTotalChange={(total) => setCalculatedTotal(total)}
              />
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-8">
      <div className="relative">
        <div className="absolute left-0 top-[15px] h-0.5 w-full bg-muted">
          <div
            className="absolute h-full bg-primary transition-all duration-500"
            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
          />
        </div>
        <div className="relative z-10 flex justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center gap-2 ${
                  index <= currentStep ? "text-primary" : "text-muted-foreground"
                }`}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full transition-colors ${
                    index <= currentStep ? "bg-primary text-primary-foreground" : "bg-muted"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                </div>
                <span className="text-sm font-medium">{step.title}</span>
              </div>
            )
          })}
        </div>
      </div>

      <Card>
        <CardHeader>
          <div>
            <CardTitle>{steps[currentStep].title}</CardTitle>
            <CardDescription>Enter the required information</CardDescription>
            {userBranchId && branchList.length > 0 && (
              <div className="mt-2 text-sm text-muted-foreground">
                <span className="font-medium">Your Branch:</span>{" "}
                {branchList.find(branch => branch.branch_id === userBranchId)?.name || "Unknown Branch"}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
        <CardFooter>
          <div className="flex w-full justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
            >
              Previous
            </Button>
            <Button
              ref={currentStep === steps.length - 1 ? submitButtonRef : undefined}
              onClick={currentStep === steps.length - 1 ? handleSubmit : handleNext}
              className="bg-primary hover:bg-primary/90"
            >
              {currentStep === steps.length - 1 ? "Submit Booking" : "Next"}
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Payment Checking Dialog */}
      <PaymentCheckingDialog
        open={isPaymentCheckingOpen}
        onOpenChange={setIsPaymentCheckingOpen}
        bookingId={bookingId}
        amount={(calculatedTotal > 0 ? calculatedTotal : formData.finalPrice).toString()}
        onSuccess={handlePaymentSuccess}
      />



      {/* Booking Confirmation Dialog */}
      <BookingConfirmationDialog
        open={isConfirmationDialogOpen}
        onOpenChange={setIsConfirmationDialogOpen}
        bookingId={bookingId}
        onClose={handleBookingComplete}
        onGenerateReceipt={handleGenerateReceipt}
        senderPhone={formData.senderPhone}
        recipientPhone={formData.recipientPhone}
        hideReceiptOption={isAdminSection}
        bookingData={{
          senderName: formData.senderName,
          recipientName: formData.recipientName,
          items: formData.items,
          totalWeight: formData.totalWeight,
          paymentMode: formData.paymentType === 'paid' ? 'Paid' : 'To Pay',
          deliveryType: deliveryTypes.find(t => t.value === formData.deliveryType)?.label || formData.deliveryType,
          finalPrice: formData.finalPrice,
          parcelTypes: parcelTypes
        }}
      />

      {/* Booking Success Page for Cash Payments */}
      {showSuccessPage && (
        <div className="fixed inset-0 z-50 bg-white">
          <BookingSuccessPage
            bookingId={bookingId}
            bookingData={{
              senderName: formData.senderName,
              recipientName: formData.recipientName,
              items: formData.items,
              totalWeight: formData.totalWeight,
              paymentMode: formData.paymentType === 'paid' ? 'Paid' : 'To Pay',
              deliveryType: deliveryTypes.find(t => t.value === formData.deliveryType)?.label || formData.deliveryType,
              finalPrice: formData.finalPrice,
              parcelTypes: parcelTypes
            }}
            senderPhone={formData.senderPhone}
            recipientPhone={formData.recipientPhone}
            onNewBooking={handleSuccessPageNewBooking}
            onGenerateReceipt={handleGenerateReceipt}
          />
        </div>
      )}
    </div>
  )
}
