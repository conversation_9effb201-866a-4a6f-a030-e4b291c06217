"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, Package, Truck, AlertTriangle, FileText, Check } from "lucide-react"

// Demo notifications data
const notifications = {
  unread: [
    {
      id: "1",
      title: "Overdue Parcel Alert",
      description: "3 parcels have exceeded the 48-hour storage limit",
      timestamp: "2024-03-21T10:30:00",
      type: "alert",
      link: "/overdue"
    },
    {
      id: "2",
      title: "Vehicle TN01BR5678 Arriving",
      description: "Scheduled to arrive in 25 minutes",
      timestamp: "2024-03-21T11:15:00",
      type: "vehicle",
      link: "/vehicles"
    },
    {
      id: "3",
      title: "New Expense Approval",
      description: "5 expenses pending your review",
      timestamp: "2024-03-21T09:45:00",
      type: "expense",
      link: "/expenses"
    }
  ],
  read: [
    {
      id: "4",
      title: "Delivery Completed",
      description: "All parcels for route CHE-CBE001 delivered successfully",
      timestamp: "2024-03-20T16:30:00",
      type: "success",
      link: "/parcels"
    },
    {
      id: "5",
      title: "System Maintenance",
      description: "Scheduled maintenance completed",
      timestamp: "2024-03-20T15:00:00",
      type: "info",
      link: "/settings"
    }
  ]
}

const typeIcons = {
  alert: AlertTriangle,
  vehicle: Truck,
  expense: FileText,
  success: Check,
  info: Bell,
  parcel: Package
}

const typeColors = {
  alert: "text-red-500",
  vehicle: "text-blue-500",
  expense: "text-purple-500",
  success: "text-green-500",
  info: "text-gray-500",
  parcel: "text-orange-500"
}

interface NotificationsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function NotificationsDialog({ open, onOpenChange }: NotificationsDialogProps) {
  const [activeTab, setActiveTab] = useState("unread")

  const NotificationItem = ({ notification }: { notification: any }) => {
    const Icon = typeIcons[notification.type as keyof typeof typeIcons]
    const colorClass = typeColors[notification.type as keyof typeof typeColors]

    return (
      <Button
        variant="ghost"
        className="w-full justify-start px-2 py-3 h-auto"
        asChild
      >
        <a href={notification.link}>
          <div className="flex items-start space-x-4">
            <Icon className={`h-5 w-5 mt-0.5 ${colorClass}`} />
            <div className="flex-1 space-y-1">
              <p className="text-sm font-medium leading-none">{notification.title}</p>
              <p className="text-sm text-muted-foreground">{notification.description}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(notification.timestamp).toLocaleString()}
              </p>
            </div>
          </div>
        </a>
      </Button>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Notifications</DialogTitle>
          <DialogDescription>
            Stay updated with your branch activities
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="unread" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="unread" className="relative">
              Unread
              <Badge
                variant="secondary"
                className="ml-2 bg-primary text-primary-foreground"
              >
                {notifications.unread.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="read">Read</TabsTrigger>
          </TabsList>
          <TabsContent value="unread" className="mt-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-4">
                {notifications.unread.map((notification) => (
                  <NotificationItem key={notification.id} notification={notification} />
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          <TabsContent value="read" className="mt-4">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-4">
                {notifications.read.map((notification) => (
                  <NotificationItem key={notification.id} notification={notification} />
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}