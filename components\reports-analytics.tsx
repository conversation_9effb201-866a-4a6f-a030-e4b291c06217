"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Search, Download, Calendar as CalendarIcon, Filter, Ch<PERSON>ronDown, <PERSON><PERSON>ronUp, FileText, Truck, IndianRupee, TrendingUp, TrendingDown, Receipt } from "lucide-react"
import { format } from "date-fns"
import { useToast } from "@/hooks/use-toast"

// Demo bookings data
const bookings = [
  {
    id: "BK001",
    lr: "LR123456",
    status: "delivered",
    originBranch: "Chennai Central",
    destinationBranch: "Mumbai Central",
    senderName: "Rajesh Kumar",
    receiverName: "Amit Shah",
    bookingDate: "2024-03-21T10:30:00",
    value: 5000,
    paymentMode: "already_paid",
    paymentStatus: "paid"
  },
  {
    id: "BK002",
    lr: "LR789012",
    status: "in_transit",
    originBranch: "Mumbai Central",
    destinationBranch: "Delhi Hub",
    senderName: "Karthik Raman",
    receiverName: "Priya Singh",
    bookingDate: "2024-03-21T14:15:00",
    value: 7500,
    paymentMode: "to_pay",
    paymentStatus: "pending"
  }
]

// Demo expenses data
const expenses = [
  {
    id: "EXP001",
    category: "fuel",
    amount: 3000,
    date: "2024-03-21T09:30:00",
    description: "Diesel refill for delivery vehicles",
    vendorName: "Indian Oil Corp",
    paymentMethod: "cash"
  },
  {
    id: "EXP002",
    category: "maintenance",
    amount: 1500,
    date: "2024-03-21T14:15:00",
    description: "Vehicle maintenance",
    vendorName: "AutoCare Services",
    paymentMethod: "cash"
  }
]

// Combined transaction type
type Transaction = {
  id: string;
  timestamp: string;
  type: 'income' | 'expense';
  amount: number;
  reference: string;
  category: string;
  description: string;
  paymentMode?: string;
  relatedParty?: string;
}

// Function to convert bookings and expenses to transactions
const convertToTransactions = () => {
  const transactions: Transaction[] = [
    // Convert bookings to transactions
    ...bookings.map(booking => ({
      id: booking.id,
      timestamp: booking.bookingDate,
      type: 'income' as const,
      amount: booking.value,
      reference: booking.lr,
      category: 'booking',
      description: `Booking from ${booking.originBranch} to ${booking.destinationBranch}`,
      paymentMode: booking.paymentMode,
      relatedParty: booking.senderName
    })),
    // Convert expenses to transactions
    ...expenses.map(expense => ({
      id: expense.id,
      timestamp: expense.date,
      type: 'expense' as const,
      amount: expense.amount,
      reference: expense.id,
      category: expense.category,
      description: expense.description,
      paymentMode: expense.paymentMethod,
      relatedParty: expense.vendorName
    }))
  ]

  return transactions
}

// Group transactions by date
const groupTransactionsByDate = (transactions: Transaction[]) => {
  const grouped = transactions.reduce<Record<string, any>>((acc, transaction) => {
    const date = transaction.timestamp.split('T')[0]
    if (!acc[date]) {
      acc[date] = {
        date,
        openingBalance: 25000, // You might want to calculate this dynamically
        transactions: [],
        closingBalance: 25000
      }
    }
    acc[date].transactions.push(transaction)
    // Update closing balance
    acc[date].closingBalance += transaction.type === 'income' ? transaction.amount : -transaction.amount
    return acc
  }, {} as Record<string, typeof ledgerEntries[0]>)

  return Object.values(grouped).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

// Create ledger entries from combined transactions
const ledgerEntries = groupTransactionsByDate(convertToTransactions())

const statusColors = {
  booked: "bg-blue-500",
  to_be_received: "bg-yellow-500",
  to_be_delivered: "bg-green-500",
  delivered: "bg-gray-500",
  cancelled: "bg-red-500"
}

const statusLabels = {
  booked: "Booked",
  to_be_received: "To Be Received",
  to_be_delivered: "To Be Delivered",
  delivered: "Delivered",
  cancelled: "Cancelled"
}

const deliveryTypes = {
  standard: "Standard Delivery",
}

export function ReportsAnalytics() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [selectedBooking, setSelectedBooking] = useState<typeof bookings[0] | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const { toast } = useToast()

  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your report is being prepared for download."
    })
  }

  const handleSort = (column: string) => {
    toast({
      title: "Sorting Applied",
      description: `Table sorted by ${column}`
    })
  }

  // Calculate total balance and expenses
  const totalBalance = ledgerEntries.reduce((sum, entry) => sum + entry.closingBalance, 0)
  const totalExpenses = ledgerEntries.reduce((sum, entry) =>
    sum + entry.transactions.reduce((expSum, exp) => expSum + (exp.type === 'expense' ? exp.amount : 0), 0), 0
  )

  return (
    <div className="space-y-4">

      <Tabs defaultValue="bookings" className="space-y-4">
        <TabsList>
          <TabsTrigger value="bookings">Booking Report</TabsTrigger>
          <TabsTrigger value="ledger">Ledger</TabsTrigger>
        </TabsList>

        <TabsContent value="bookings">
          <div className="space-y-4">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by LR number..."
                    className="pl-8"
                  />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    {Object.entries(statusLabels).map(([value, label]) => (
                      <SelectItem key={value} value={value}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Delivery type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {Object.entries(deliveryTypes).map(([value, label]) => (
                      <SelectItem key={value} value={value}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Booking Report</CardTitle>
                <CardDescription>
                  Detailed view of all bookings and their current status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[100px] cursor-pointer" onClick={() => handleSort("lr")}>
                          LR Number
                          <ChevronDown className="ml-2 h-4 w-4 inline-block" />
                        </TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Origin → Destination</TableHead>
                        <TableHead>Sender → Receiver</TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("bookingDate")}>
                          Booking Date
                          <ChevronDown className="ml-2 h-4 w-4 inline-block" />
                        </TableHead>
                        <TableHead className="text-right cursor-pointer" onClick={() => handleSort("value")}>
                          Value
                          <ChevronDown className="ml-2 h-4 w-4 inline-block" />
                        </TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {bookings.map((booking) => (
                        <TableRow key={booking.lr}>
                          <TableCell className="font-medium">{booking.lr}</TableCell>
                          <TableCell>
                            <Badge className={statusColors[booking.status as keyof typeof statusColors]}>
                              {statusLabels[booking.status as keyof typeof statusLabels]}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {booking.originBranch} → {booking.destinationBranch}
                          </TableCell>
                          <TableCell>
                            {booking.senderName} → {booking.receiverName}
                          </TableCell>
                          <TableCell>
                            {format(new Date(booking.bookingDate), "PPp")}
                          </TableCell>
                          <TableCell className="text-right">₹{booking.value}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedBooking(booking)
                                setIsDetailsOpen(true)
                              }}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ledger">
          <div className="space-y-4">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex items-center space-x-4">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export Ledger
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Balance
                  </CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹{totalBalance.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Sum of all closing balances
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Expenses
                  </CardTitle>
                  <Receipt className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-500">₹{totalExpenses.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Total expenses across all entries
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Daily Ledger</CardTitle>
                <CardDescription>Daily income, expenses, and detailed transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {ledgerEntries.map((entry) => (
                    <div key={entry.date} className="border rounded-lg">
                      {/* Date Header */}
                      <div className="bg-muted p-4 rounded-t-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold">{format(new Date(entry.date), "PPP")}</h3>
                            <p className="text-sm text-muted-foreground">Opening Balance: ₹{entry.openingBalance.toLocaleString()}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">Closing Balance: ₹{entry.closingBalance.toLocaleString()}</p>
                          </div>
                        </div>
                      </div>

                      {/* Transactions */}
                      <div className="p-4">
                        <div className="space-y-4">
                          {entry.transactions
                            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
                            .map((transaction) => (
                              <div key={transaction.id} className="flex items-center justify-between border-b pb-3">
                                <div className="space-y-1">
                                  <div className="flex items-center space-x-2">
                                    <Badge variant={transaction.type === 'income' ? 'default' : 'destructive'}>
                                      {transaction.type === 'income' ? 'Income' : 'Expense'}
                                    </Badge>
                                    <span className="text-sm text-muted-foreground">
                                      {format(new Date(transaction.timestamp), "h:mm a")}
                                    </span>
                                  </div>
                                  <p className="font-medium">{transaction.description}</p>
                                  <p className="text-sm text-muted-foreground">
                                    Ref: {transaction.reference} | {transaction.category} | {transaction.relatedParty}
                                  </p>
                                </div>
                                <div className="text-right">
                                  <p className={`font-semibold ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                                    {transaction.type === 'income' ? '+' : '-'}₹{transaction.amount.toLocaleString()}
                                  </p>
                                  <p className="text-sm text-muted-foreground">{transaction.paymentMode}</p>
                                </div>
                              </div>
                            ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {selectedBooking && (
        <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Booking Details - {selectedBooking.lr}</DialogTitle>
              <DialogDescription>
                Complete journey and tracking information
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="max-h-[600px]">
              <div className="space-y-6 p-4">
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Booking Information</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">Status:</span> {statusLabels[selectedBooking.status as keyof typeof statusLabels]}</p>
                      <p><span className="font-medium">Delivery Type:</span> {deliveryTypes[selectedBooking.deliveryType as keyof typeof deliveryTypes]}</p>
                      <p><span className="font-medium">Payment Mode:</span> {selectedBooking.paymentMode === "paid" ? "Paid" : "To Pay"}</p>
                      <p><span className="font-medium">Value:</span> ₹{selectedBooking.value}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Route Information</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">Origin:</span> {selectedBooking.originBranch}</p>
                      <p><span className="font-medium">Destination:</span> {selectedBooking.destinationBranch}</p>
                      <p><span className="font-medium">Vehicle:</span> {selectedBooking.vehicle}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Tracking Timeline</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                        <FileText className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div>
                        <p className="font-medium">Booking Created</p>
                        <p className="text-sm text-muted-foreground">{format(new Date(selectedBooking.bookingDate), "PPp")}</p>
                      </div>
                    </div>
                    {selectedBooking.vehicle && (
                      <div className="flex items-center space-x-4">
                        <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                          <Truck className="h-4 w-4 text-primary-foreground" />
                        </div>
                        <div>
                          <p className="font-medium">Assigned to Vehicle</p>
                          <p className="text-sm text-muted-foreground">Vehicle ID: {selectedBooking.vehicle}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </ScrollArea>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
