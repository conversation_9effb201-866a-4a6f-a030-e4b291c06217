"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, Package, User, MapPin, CreditCard, FileText, Printer, ArrowLeft } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface BookingSuccessPageProps {
  bookingId: string
  bookingData: {
    senderName: string
    recipientName: string
    items: Array<{
      id: number
      type: string
      count: number
      weight: string
    }>
    totalWeight: string
    paymentMode: string
    deliveryType: string
    finalPrice: number
    parcelTypes: Array<{
      type_id: number
      type_name: string
    }>
  }
  senderPhone: string
  recipientPhone: string
  onNewBooking: () => void
  onGenerateReceipt?: () => void
}

export function BookingSuccessPage({
  bookingId,
  bookingData,
  sender<PERSON><PERSON>,
  recipient<PERSON><PERSON>,
  onNewBooking,
  onGenerateReceipt
}: BookingSuccessPageProps) {
  const { toast } = useToast()
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false)

  const handleGenerateReceipt = async () => {
    setIsGeneratingReceipt(true)
    try {
      if (onGenerateReceipt) {
        onGenerateReceipt()
      }
      toast({
        title: "Receipt Generated",
        description: "Receipt has been generated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate receipt",
        variant: "destructive",
      })
    } finally {
      setIsGeneratingReceipt(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-10 h-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Booking Completed Successfully!
          </h1>
          <p className="text-lg text-gray-600">
            Your parcel has been booked and is ready for dispatch
          </p>
        </div>

        {/* Booking Details Card */}
        <Card className="mb-6">
          <CardHeader className="text-center bg-green-50">
            <CardTitle className="text-xl text-green-800">
              Booking Reference: {bookingId}
            </CardTitle>
            <CardDescription>
              Please save this reference number for tracking
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Sender & Recipient Info */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-blue-600" />
                  <h3 className="font-semibold text-gray-900">Sender Details</h3>
                </div>
                <div className="pl-6 space-y-1">
                  <p className="font-medium">{bookingData.senderName}</p>
                  <p className="text-sm text-gray-600">+91 {senderPhone}</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-green-600" />
                  <h3 className="font-semibold text-gray-900">Recipient Details</h3>
                </div>
                <div className="pl-6 space-y-1">
                  <p className="font-medium">{bookingData.recipientName}</p>
                  <p className="text-sm text-gray-600">+91 {recipientPhone}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Parcel Details */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-orange-600" />
                <h3 className="font-semibold text-gray-900">Parcel Details</h3>
              </div>
              <div className="pl-6 space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Items:</span>
                  <div className="text-right">
                    {bookingData.items.map((item, index) => (
                      <div key={item.id} className="text-sm">
                        {item.count} × {bookingData.parcelTypes.find(t => t.type_id.toString() === item.type)?.type_name || 'Item'}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Weight:</span>
                  <span className="text-sm font-medium">{bookingData.totalWeight} kg</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Delivery Type:</span>
                  <span className="text-sm font-medium">{bookingData.deliveryType}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Payment Details */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CreditCard className="w-4 h-4 text-purple-600" />
                <h3 className="font-semibold text-gray-900">Payment Details</h3>
              </div>
              <div className="pl-6 space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Payment Mode:</span>
                  <Badge variant={bookingData.paymentMode === "Paid" ? "default" : "secondary"}>
                    {bookingData.paymentMode}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Amount:</span>
                  <span className="text-lg font-bold text-green-600">₹{bookingData.finalPrice.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            onClick={onNewBooking}
            className="flex items-center gap-2"
            size="lg"
          >
            <ArrowLeft className="w-4 h-4" />
            New Booking
          </Button>
          
          {onGenerateReceipt && (
            <Button
              variant="outline"
              onClick={handleGenerateReceipt}
              disabled={isGeneratingReceipt}
              className="flex items-center gap-2"
              size="lg"
            >
              <FileText className="w-4 h-4" />
              {isGeneratingReceipt ? "Generating..." : "Generate Receipt"}
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
            size="lg"
          >
            <Printer className="w-4 h-4" />
            Print Details
          </Button>
        </div>

        {/* Additional Info */}
        <div className="text-center mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Next Steps:</strong> Your parcel will be processed and dispatched according to the selected delivery type. 
            You can track your parcel using the booking reference number.
          </p>
        </div>
      </div>
    </div>
  )
}
