import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/loading-charts/[id]/items
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Get loading chart items with parcel details
    // Note: We'll use a simpler approach to avoid foreign key issues
    const { data: items, error } = await supabase
      .from("loading_chart_items")
      .select("*")
      .eq("chart_id", id)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error fetching loading chart items:", error);
      return NextResponse.json(
        { error: "Failed to fetch loading chart items" },
        {
          status: 500,
        },
      );
    }

    // Fetch parcel details separately for each item
    const itemsWithParcels = await Promise.all(
      (items || []).map(async (item) => {
        const { data: parcel, error: parcelError } = await supabase
          .from("parcels")
          .select(`
            parcel_id,
            sender_name,
            recipient_name,
            sender_phone,
            recipient_phone,
            current_status,
            total_items,
            payment_mode,
            sender_branch:branches!parcels_sender_branch_id_fkey (
              name,
              code
            ),
            delivery_branch:branches!parcels_delivery_branch_id_fkey (
              name,
              code
            )
          `)
          .eq("lr_number", item.lr_number)
          .single();

        return {
          ...item,
          parcel: parcelError ? null : parcel,
        };
      }),
    );

    return NextResponse.json({ items: itemsWithParcels });
  } catch (error: any) {
    console.error("Error in GET /api/loading-charts/[id]/items:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
