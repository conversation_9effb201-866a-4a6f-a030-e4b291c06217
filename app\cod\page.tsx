"use client"

import { useState } from "react"
import { MainNav } from "@/components/main-nav"
import { UserNav } from "@/components/user-nav"
import { MobileNav } from "@/components/mobile-nav"
import { CashManagement } from "@/components/cash-management"
import { PageLayout } from "@/components/page-layout"
import { DashboardShell } from "@/components/dashboard-shell"

export default function CashManagementPage() {
  return (
    <PageLayout
      title="Cash Management"
      subtitle="Manage cash collections and remittances to head office"
    >
      <DashboardShell>
        <CashManagement />
      </DashboardShell>
    </PageLayout>
  )
}