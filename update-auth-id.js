const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://nekjeqxlwhfwyekeinnc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y'
);

async function updateAuthId() {
  try {
    console.log('Updating auth_id for the missing user...');
    
    const missingAuthId = 'c18a88ae-8e04-4576-b960-06ca18dd8f1a';
    const userEmail = '<EMAIL>';
    
    console.log('Auth ID to assign:', missingAuthId);
    console.log('User email:', userEmail);
    
    // First, let's verify the user exists and has no auth_id
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single();
      
    if (checkError) {
      console.error('Error finding user:', checkError);
      return;
    }
    
    console.log('Found user:', {
      user_id: existingUser.user_id,
      email: existingUser.email,
      name: existingUser.name,
      role: existingUser.role,
      branch_id: existingUser.branch_id,
      current_auth_id: existingUser.auth_id
    });
    
    if (existingUser.auth_id) {
      console.log('WARNING: User already has an auth_id:', existingUser.auth_id);
      console.log('Are you sure you want to update it to:', missingAuthId);
      return;
    }
    
    // Update the auth_id
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({ auth_id: missingAuthId })
      .eq('email', userEmail)
      .select()
      .single();
      
    if (updateError) {
      console.error('Error updating auth_id:', updateError);
      return;
    }
    
    console.log('Successfully updated user:');
    console.log('Updated user:', {
      user_id: updatedUser.user_id,
      email: updatedUser.email,
      name: updatedUser.name,
      role: updatedUser.role,
      branch_id: updatedUser.branch_id,
      auth_id: updatedUser.auth_id
    });
    
    // Verify the fix by trying to find the user by auth_id
    console.log('\nVerifying the fix...');
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('user_id, email, name, role, branch_id')
      .eq('auth_id', missingAuthId)
      .single();
      
    if (verifyError) {
      console.error('Verification failed:', verifyError);
    } else {
      console.log('✅ Fix verified! User can now be found by auth_id:');
      console.log(verifyUser);
    }
    
  } catch (err) {
    console.error('Error:', err);
  }
}

updateAuthId();
