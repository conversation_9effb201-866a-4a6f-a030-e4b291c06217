import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// Helper function to generate a loading chart number
function generateLoadingChartNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0");
  return `LC${year}${month}${day}${random}`;
}

// GET /api/loading-charts
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const memo_id = url.searchParams.get("memo_id");
    const vehicle_id = url.searchParams.get("vehicle_id");
    const status = url.searchParams.get("status");
    const destination_branch_id = url.searchParams.get("destination_branch_id");
    const date = url.searchParams.get("date");
    const search = url.searchParams.get("search");
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20");

    // Build query with parcel counts
    let query = supabase
      .from("loading_charts")
      .select(
        `
        *,
        memo:memos(memo_number),
        vehicle:vehicles(registration_number, vehicle_type),
        destination_branch:branches(name, code),
        loading_chart_items(
          item_id,
          quantity
        )
      `,
        { count: "exact" },
      );

    // Add filters if provided
    if (memo_id) {
      query = query.eq("memo_id", memo_id);
    }

    if (vehicle_id) {
      query = query.eq("vehicle_id", vehicle_id);
    }

    if (status) {
      query = query.eq("status", status);
    }

    if (destination_branch_id) {
      query = query.eq("destination_branch_id", destination_branch_id);
    }

    if (date) {
      // Filter by date (created_at)
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);

      query = query
        .gte("created_at", startDate.toISOString())
        .lt("created_at", endDate.toISOString());
    }

    if (search) {
      // Search in chart_number
      query = query.ilike("chart_number", `%${search}%`);
    }

    // Order by created_at
    query = query.order("created_at", { ascending: false });

    // Add pagination
    const offset = (page - 1) * pageSize;
    query = query.range(offset, offset + pageSize - 1);

    // Execute query
    const { data: charts, error, count } = await query;

    if (error) {
      console.error("Error fetching loading charts:", error);
      return NextResponse.json({ error: "Failed to fetch loading charts" }, {
        status: 500,
      });
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / pageSize);

    // Calculate parcel counts for each chart
    const chartsWithCounts = charts?.map((chart) => {
      const items = chart.loading_chart_items || [];
      const totalParcels = items.length;
      const loadedParcels = items.reduce(
        (sum: number, item: any) => sum + (item.quantity || 0),
        0,
      );

      return {
        ...chart,
        total_parcels: totalParcels,
        loaded_parcels: loadedParcels,
        // Remove the items array to keep response clean
        loading_chart_items: undefined,
      };
    }) || [];

    return NextResponse.json({
      charts: chartsWithCounts,
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error: any) {
    console.error("Error in GET /api/loading-charts:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// POST /api/loading-charts
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (
      !body.memo_id || !body.vehicle_id || !body.destination_branch_id ||
      !body.lr_entries || body.lr_entries.length === 0
    ) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: memo_id, vehicle_id, destination_branch_id, lr_entries",
        },
        { status: 400 },
      );
    }

    // Start a transaction
    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Generate loading chart number
    const chart_number = generateLoadingChartNumber();

    // Create loading chart
    const { data: chart, error: chartError } = await supabase
      .from("loading_charts")
      .insert({
        chart_number,
        memo_id: body.memo_id,
        vehicle_id: body.vehicle_id,
        destination_branch_id: body.destination_branch_id,
        created_by: user?.id || null,
        status: "Created",
      })
      .select()
      .single();

    if (chartError) {
      console.error("Error creating loading chart:", chartError);
      return NextResponse.json({ error: "Failed to create loading chart" }, {
        status: 500,
      });
    }

    // Create loading chart items
    const chartItems = body.lr_entries.map((entry: any) => ({
      chart_id: chart.chart_id,
      lr_number: entry.lr_number,
      quantity: entry.quantity,
      status: "Pending",
    }));

    const { error: itemsError } = await supabase
      .from("loading_chart_items")
      .insert(chartItems);

    if (itemsError) {
      console.error("Error creating loading chart items:", itemsError);
      return NextResponse.json({
        error: "Failed to create loading chart items",
      }, {
        status: 500,
      });
    }

    // Update parcel status to "In Transit"
    for (const entry of body.lr_entries) {
      const { error: parcelError } = await supabase
        .from("parcels")
        .update({ current_status: "In Transit" })
        .eq("lr_number", entry.lr_number);

      if (parcelError) {
        console.error(`Error updating parcel ${entry.lr_number}:`, parcelError);
        // Continue with other parcels even if one fails
      }
    }

    return NextResponse.json({
      message: "Loading chart created successfully",
      chart_id: chart.chart_id,
      chart_number: chart.chart_number,
    });
  } catch (error: any) {
    console.error("Error in POST /api/loading-charts:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
