import { NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

// GET /api/charge-settings/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const id = parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid setting ID' }, { status: 400 })
    }

    const { data: setting, error } = await supabase
      .from('charge_settings')
      .select('*')
      .eq('setting_id', id)
      .single()

    if (error) {
      console.error('Error fetching charge setting:', error)
      return NextResponse.json({ error: 'Charge setting not found' }, { status: 404 })
    }

    return NextResponse.json(setting)
  } catch (error: any) {
    console.error(`Error in GET /api/charge-settings/${params.id}:`, error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// PUT /api/charge-settings/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid setting ID' }, { status: 400 })
    }

    const body = await request.json()

    // Validate setting_type if provided
    if (body.setting_type && !['percentage', 'flat'].includes(body.setting_type)) {
      return NextResponse.json(
        { error: 'setting_type must be either "percentage" or "flat"' },
        { status: 400 }
      )
    }

    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    // Only update provided fields
    if (body.setting_name !== undefined) updateData.setting_name = body.setting_name
    if (body.setting_type !== undefined) updateData.setting_type = body.setting_type
    if (body.setting_value !== undefined) updateData.setting_value = body.setting_value
    if (body.description !== undefined) updateData.description = body.description
    if (body.is_active !== undefined) updateData.is_active = body.is_active

    const { data: setting, error } = await supabase
      .from('charge_settings')
      .update(updateData)
      .eq('setting_id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating charge setting:', error)
      return NextResponse.json({ error: 'Failed to update charge setting' }, { status: 500 })
    }

    return NextResponse.json(setting)
  } catch (error: any) {
    console.error(`Error in PUT /api/charge-settings/${params.id}:`, error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

// DELETE /api/charge-settings/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid setting ID' }, { status: 400 })
    }

    // Soft delete by setting is_active to false
    const { data: setting, error } = await supabase
      .from('charge_settings')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('setting_id', id)
      .select()
      .single()

    if (error) {
      console.error('Error deleting charge setting:', error)
      return NextResponse.json({ error: 'Failed to delete charge setting' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Charge setting deleted successfully' })
  } catch (error: any) {
    console.error(`Error in DELETE /api/charge-settings/${params.id}:`, error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
