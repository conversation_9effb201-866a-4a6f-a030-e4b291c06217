"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"

// Demo data
const transactions = [
  {
    id: "TXN001",
    lr: "LR123456",
    amount: 5000,
    collectedAt: "2024-03-21T10:30:00",
    collectedBy: "<PERSON><PERSON>",
    status: "collected",
    customerName: "Priya Sundaram",
    customerPhone: "+91 98765 43210",
    paymentMode: "cash"
  },
  {
    id: "TXN002",
    lr: "LR789012",
    amount: 2500,
    collectedAt: "2024-03-21T11:45:00",
    collectedBy: "<PERSON><PERSON><PERSON>",
    status: "remitted",
    customerName: "<PERSON><PERSON><PERSON> <PERSON>n",
    customerPhone: "+91 87654 32109",
    paymentMode: "upi"
  },
  {
    id: "TXN003",
    lr: "LR345678",
    amount: 7500,
    status: "pending",
    customerName: "Lakshmi Venkatesh",
    customerPhone: "+91 76543 21098",
    paymentMode: "pending"
  }
]

const statusColors = {
  pending: "bg-yellow-500",
  collected: "bg-green-500",
  remitted: "bg-blue-500"
}

const statusLabels = {
  pending: "Pending Collection",
  collected: "Collected",
  remitted: "Remitted"
}

const paymentModes = {
  cash: "Cash",
  upi: "UPI Payment",
  card: "Card Payment",
  pending: "Pending"
}

export function CashManagement() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")

  const handleStatusUpdate = (transactionId: string, newStatus: string) => {
    toast({
      title: "Status Updated",
      description: `Transaction ${transactionId} has been marked as ${newStatus}.`
    })
  }

  return (
    <div className="space-y-4">

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="relative flex-1 md:max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by LR or customer..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="collected">Collected</SelectItem>
            <SelectItem value="remitted">Remitted</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-6">
        {transactions.map((transaction) => (
          <Card key={transaction.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-base font-medium">
                  {transaction.id} - LR: {transaction.lr}
                </CardTitle>
                <CardDescription>
                  Amount: ₹{transaction.amount.toLocaleString()}
                </CardDescription>
              </div>
              <Badge className={statusColors[transaction.status as keyof typeof statusColors]}>
                {statusLabels[transaction.status as keyof typeof statusLabels]}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Customer Details</p>
                  <p className="text-sm text-muted-foreground">{transaction.customerName}</p>
                  <p className="text-sm text-muted-foreground">{transaction.customerPhone}</p>
                </div>
                {transaction.collectedAt && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Collection Details</p>
                    <p className="text-sm text-muted-foreground">
                      Collected by: {transaction.collectedBy}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      On: {new Date(transaction.collectedAt).toLocaleString()}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Mode: {paymentModes[transaction.paymentMode as keyof typeof paymentModes]}
                    </p>
                  </div>
                )}
              </div>
              <div className="mt-4 flex items-center gap-2">
                {transaction.status === "pending" && (
                  <Button
                    onClick={() => handleStatusUpdate(transaction.id, "collected")}
                  >
                    Mark as Collected
                  </Button>
                )}
                {transaction.status === "collected" && (
                  <Button
                    onClick={() => handleStatusUpdate(transaction.id, "remitted")}
                  >
                    Mark as Remitted
                  </Button>
                )}
                <Button variant="outline">View Details</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}