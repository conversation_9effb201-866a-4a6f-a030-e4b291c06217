"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Package, Truck, Receipt, AlertTriangle, Clock, IndianRupee } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from 'next/navigation'

export function Overview() {
  const router = useRouter()

  return (
    <div className="space-y-4">

      <div className="grid gap-3 grid-cols-2 md:grid-cols-4">
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">
              Active Parcels
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-2xl font-bold">127</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>82 for pickup, 45 for dispatch</span>
            </div>
          </CardContent>
          <Button
            variant="ghost"
            className="absolute inset-0 h-full w-full opacity-0"
            onClick={() => router.push('/parcels')}
          >
            <span className="sr-only">View parcels</span>
          </Button>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">
              Scheduled Vehicles
            </CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-2xl font-bold">8</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="mr-1 h-3 w-3" />
              <span>Next arrival in 25 mins</span>
            </div>
          </CardContent>
          <Button
            variant="ghost"
            className="absolute inset-0 h-full w-full opacity-0"
            onClick={() => router.push('/vehicles')}
          >
            <span className="sr-only">View vehicles</span>
          </Button>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">
              Accounts Summary
            </CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-2xl font-bold">₹15,750</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>23 transactions today</span>
            </div>
          </CardContent>
          <Button
            variant="ghost"
            className="absolute inset-0 h-full w-full opacity-0"
            onClick={() => router.push('/operations')}
          >
            <span className="sr-only">View operations</span>
          </Button>
        </Card>

        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Pending Expenses</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3 pt-0 px-3">
            <div className="text-2xl font-bold">₹4,891</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <span>12 receipts to review</span>
            </div>
          </CardContent>
          <Button
            variant="ghost"
            className="absolute inset-0 h-full w-full opacity-0"
            onClick={() => router.push('/operations')}
          >
            <span className="sr-only">View expenses</span>
          </Button>
        </Card>
      </div>

      <div className="grid gap-3 grid-cols-1 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Attention Required</CardTitle>
          </CardHeader>
          <CardContent className="px-3 py-0">
            <div className="space-y-2">
              <Button
                variant="ghost"
                className="w-full justify-between rounded-lg border p-2 text-left text-sm"
                onClick={() => router.push('/parcels')}
              >
                <div className="flex items-center space-x-4">
                  <AlertTriangle className="h-5 w-5 text-destructive" />
                  <div>
                    <p className="text-sm font-medium">3 Overdue Parcels</p>
                    <p className="text-xs text-muted-foreground">Exceeding 48-hour storage limit</p>
                  </div>
                </div>
                <span className="sr-only">View overdue parcels</span>
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-between rounded-lg border p-2 text-left text-sm"
                onClick={() => router.push('/vehicles')}
              >
                <div className="flex items-center space-x-4">
                  <Clock className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium">Vehicle TN01BR5678 Arriving Soon</p>
                    <p className="text-xs text-muted-foreground">Scheduled arrival in 25 minutes</p>
                  </div>
                </div>
                <span className="sr-only">View vehicles</span>
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-between rounded-lg border p-2 text-left text-sm"
                onClick={() => router.push('/operations')}
              >
                <div className="flex items-center space-x-4">
                  <Receipt className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">5 Expenses Pending Review</p>
                    <p className="text-xs text-muted-foreground">Awaiting supervisor approval</p>
                  </div>
                </div>
                <span className="sr-only">View expenses</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="px-3 py-2">
            <div className="grid gap-2 grid-cols-2">
              <Button
                variant="outline"
                className="h-16 flex-col text-xs py-1"
                onClick={() => router.push('/parcels?action=new')}
              >
                <Package className="h-5 w-5 mb-1" />
                New Booking
              </Button>
              <Button
                variant="outline"
                className="h-16 flex-col text-xs py-1"
                onClick={() => router.push('/vehicles?action=receive')}
              >
                <Truck className="h-5 w-5 mb-1" />
                Receive Parcels
              </Button>
              <Button
                variant="outline"
                className="h-16 flex-col text-xs py-1"
                onClick={() => router.push('/vehicles?action=load')}
              >
                <Package className="h-5 w-5 mb-1" />
                Load Parcels
              </Button>
              <Button
                variant="outline"
                className="h-16 flex-col text-xs py-1"
                onClick={() => router.push('/parcels?tab=manage&view=all&focus=search')}
              >
                <Receipt className="h-5 w-5 mb-1" />
                Search Parcel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
