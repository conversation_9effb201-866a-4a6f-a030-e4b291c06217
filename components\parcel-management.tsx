"use client"

import { useState, useEffect } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { NewParcelBooking } from "@/components/new-parcel-booking"
import { Pa<PERSON>elList } from "@/components/parcel-list"
import { LoadingChartView } from "@/components/loading-chart-view"
import { useToast } from "@/hooks/use-toast"

export function ParcelManagement() {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get tab from URL parameters, default to "manage"
  const tabFromUrl = searchParams.get('tab') || 'manage';
  const [activeTab, setActiveTab] = useState(tabFromUrl);

  // Update tab when URL parameters change (only on initial load)
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && tab !== activeTab) {
      setActiveTab(tab);
    }
  }, [searchParams, activeTab]);

  // Handle tab change - clear URL parameters when switching tabs manually
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Clear URL parameters when switching tabs manually
    if (newTab !== 'manage' || !searchParams.get('view')) {
      router.replace('/parcels');
    }
  };

  // Seed default parcel types on component mount
  useEffect(() => {
    const seedParcelTypes = async () => {
      try {
        const response = await fetch('/api/parceltypes/seed', {
          method: 'POST',
        });

        if (!response.ok) {
          console.error('Failed to seed parcel types');
          // Don't show an error toast to the user as this is a background operation
        } else {
          const data = await response.json();
          console.log('Parcel types seed result:', data.message);
        }
      } catch (error: any) {
        console.error('Error seeding parcel types:', error);
        // Don't show an error toast to the user as this is a background operation
      }
    };

    seedParcelTypes();
  }, []);

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList>
          <TabsTrigger value="new">New Booking</TabsTrigger>
          <TabsTrigger value="manage">Manage Parcels</TabsTrigger>
          <TabsTrigger value="loading-charts">Loading Charts</TabsTrigger>
        </TabsList>
        <TabsContent value="manage" className="space-y-4">
          <ParcelList />
        </TabsContent>
        <TabsContent value="loading-charts" className="space-y-4">
          <LoadingChartView />
        </TabsContent>
        <TabsContent value="new" className="space-y-4">
          <NewParcelBooking />
        </TabsContent>
      </Tabs>
    </div>
  )
}