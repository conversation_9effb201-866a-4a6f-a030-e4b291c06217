import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/branches/current
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const branch_id = url.searchParams.get("branch_id");

    if (!branch_id) {
      return NextResponse.json(
        { error: "Branch ID is required" },
        { status: 400 }
      );
    }

    // Get branch details
    const { data: branch, error: branchError } = await supabase
      .from("branches")
      .select(`
        branch_id,
        name,
        code,
        address,
        city_id,
        status,
        branch_type,
        city:cities(name)
      `)
      .eq("branch_id", branch_id)
      .single();

    if (branchError || !branch) {
      console.error("Error finding branch:", branchError);
      return NextResponse.json(
        { error: "Branch not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      branch: {
        branch_id: branch.branch_id,
        name: branch.name,
        code: branch.code,
        address: branch.address,
        city_name: branch.city?.name || "Unknown",
        status: branch.status,
        branch_type: branch.branch_type
      }
    });

  } catch (error: any) {
    console.error("Error in GET /api/branches/current:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
