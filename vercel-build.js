/**
 * Build script for Vercel deployment
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// Load environment variables
require("dotenv").config();

console.log("Starting Vercel build process...");

// Ensure Supabase environment variables are set
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_URL = "https://nekjeqxlwhfwyekeinnc.supabase.co";
  console.log("Set default NEXT_PUBLIC_SUPABASE_URL");
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
  console.log("Set default NEXT_PUBLIC_SUPABASE_ANON_KEY");
}

console.log("Environment variables:");
console.log(`- NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL}`);
console.log(`- NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10)}...`);

// Create a minimal next.config.js for the build
const nextConfig = `/** @type {import('next').NextConfig} */
module.exports = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: { unoptimized: true },
  env: {
    NEXT_PUBLIC_SUPABASE_URL: "${process.env.NEXT_PUBLIC_SUPABASE_URL}",
    NEXT_PUBLIC_SUPABASE_ANON_KEY: "${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}"
  },
  webpack: (config, { dev, isServer }) => {
    if (dev && isServer) {
      config.cache = { type: "memory" };
    }
    return config;
  }
};`;

// Backup the original next.config.js
const configPath = path.join(process.cwd(), "next.config.js");
const backupPath = path.join(process.cwd(), "next.config.js.bak");

try {
  if (fs.existsSync(configPath)) {
    console.log("Backing up original next.config.js...");
    fs.copyFileSync(configPath, backupPath);
  }

  // Write the minimal config
  console.log("Writing next.config.js with environment variables...");
  fs.writeFileSync(configPath, nextConfig, "utf8");

  // Run the build
  console.log("Running Next.js build...");
  execSync("npx next build", {
    stdio: "inherit",
    env: {
      ...process.env,
      NODE_OPTIONS: "--max-old-space-size=4096",
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    }
  });

  console.log("Build completed successfully!");
} catch (error) {
  console.error("Build failed:", error.message);
  process.exit(1);
} finally {
  // Restore the original next.config.js
  if (fs.existsSync(backupPath)) {
    console.log("Restoring original next.config.js...");
    fs.copyFileSync(backupPath, configPath);
    fs.unlinkSync(backupPath);
  }
}
