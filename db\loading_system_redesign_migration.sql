-- Loading System Redesign Migration
-- This migration updates the loading system to support destination selection and Direct/Via loading

-- Step 1: Update loading_charts table structure
ALTER TABLE loading_charts 
ADD COLUMN IF NOT EXISTS loading_type VARCHAR(20) CHECK (loading_type IN ('Direct', 'Via')),
ADD COLUMN IF NOT EXISTS destination_city_id INT REFERENCES cities(city_id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS total_parcels INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS loaded_parcels INT DEFAULT 0;

-- Make memo_id optional since loading will be independent of memos
ALTER TABLE loading_charts 
ALTER COLUMN memo_id DROP NOT NULL;

-- Step 2: Add loading_type to loading_chart_items for tracking
ALTER TABLE loading_chart_items
ADD COLUMN IF NOT EXISTS loading_type VARCHAR(20) CHECK (loading_type IN ('Direct', 'Via')),
ADD COLUMN IF NOT EXISTS destination_branch_id INT REFERENCES branches(branch_id) ON DELETE SET NULL;

-- Step 3: Create new function for loading operations that creates parcel actions
CREATE OR REPLACE FUNCTION process_parcel_loading()
RETURNS TRIGGER AS $$
DECLARE
  v_parcel_id INT;
  v_parcel_delivery_branch_id INT;
  v_loading_destination_branch_id INT;
  v_loading_type VARCHAR(20);
  v_user_id INT;
  v_chart_number VARCHAR(50);
BEGIN
  -- Get parcel information
  SELECT p.parcel_id, p.delivery_branch_id 
  INTO v_parcel_id, v_parcel_delivery_branch_id
  FROM parcels p 
  WHERE p.lr_number = NEW.lr_number;
  
  -- Get loading chart information
  SELECT lc.destination_branch_id, lc.chart_number
  INTO v_loading_destination_branch_id, v_chart_number
  FROM loading_charts lc 
  WHERE lc.chart_id = NEW.chart_id;
  
  -- Determine loading type based on destination match
  IF v_parcel_delivery_branch_id = v_loading_destination_branch_id THEN
    v_loading_type := 'Direct';
  ELSE
    v_loading_type := 'Via';
  END IF;
  
  -- Update the loading chart item with loading type and destination
  UPDATE loading_chart_items 
  SET 
    loading_type = v_loading_type,
    destination_branch_id = v_loading_destination_branch_id
  WHERE item_id = NEW.item_id;
  
  -- Update parcel status to 'Loaded'
  UPDATE parcels 
  SET current_status = 'Loaded'
  WHERE parcel_id = v_parcel_id;
  
  -- Get user ID from auth user
  SELECT user_id INTO v_user_id
  FROM users 
  WHERE auth_id = (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id);
  
  -- Create parcel action record
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    vehicle_id,
    operator_id,
    destination_branch_id,
    loading_type,
    quantity_loaded,
    remarks,
    reference_number,
    created_by
  )
  SELECT
    v_parcel_id,
    'Loaded',
    NOW(),
    (SELECT branch_id FROM users WHERE user_id = v_user_id), -- Current user's branch
    (SELECT name FROM branches WHERE branch_id = (SELECT branch_id FROM users WHERE user_id = v_user_id)),
    lc.vehicle_id,
    v_user_id,
    v_loading_destination_branch_id,
    v_loading_type,
    NEW.quantity,
    'Loaded via chart ' || v_chart_number || ' (' || v_loading_type || ' loading)',
    v_chart_number,
    (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id)
  FROM loading_charts lc
  WHERE lc.chart_id = NEW.chart_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create new function for receiving operations
CREATE OR REPLACE FUNCTION process_parcel_receiving()
RETURNS TRIGGER AS $$
DECLARE
  v_parcel_id INT;
  v_parcel_delivery_branch_id INT;
  v_current_branch_id INT;
  v_new_status parcel_status;
  v_user_id INT;
BEGIN
  -- Get parcel information
  SELECT p.parcel_id, p.delivery_branch_id 
  INTO v_parcel_id, v_parcel_delivery_branch_id
  FROM parcels p 
  WHERE p.lr_number = NEW.lr_number;
  
  -- Get current user's branch
  SELECT branch_id INTO v_current_branch_id
  FROM users 
  WHERE auth_id = NEW.received_by;
  
  -- Determine new status based on whether parcel reached final destination
  IF v_parcel_delivery_branch_id = v_current_branch_id THEN
    v_new_status := 'Delivered'; -- Parcel reached final destination
  ELSE
    v_new_status := 'Received'; -- Parcel at intermediate branch
  END IF;
  
  -- Update parcel status
  UPDATE parcels 
  SET current_status = v_new_status
  WHERE parcel_id = v_parcel_id;
  
  -- Get user ID
  SELECT user_id INTO v_user_id
  FROM users 
  WHERE auth_id = NEW.received_by;
  
  -- Create parcel action record
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    vehicle_id,
    operator_id,
    quantity_received,
    remarks,
    reference_number,
    created_by
  )
  SELECT
    v_parcel_id,
    v_new_status::VARCHAR(50),
    NOW(),
    v_current_branch_id,
    (SELECT name FROM branches WHERE branch_id = v_current_branch_id),
    lc.vehicle_id,
    v_user_id,
    NEW.quantity,
    CASE 
      WHEN v_new_status = 'Delivered' THEN 'Delivered at final destination'
      ELSE 'Received at intermediate branch'
    END,
    lc.chart_number,
    NEW.received_by
  FROM loading_charts lc
  WHERE lc.chart_id = NEW.chart_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Update triggers
DROP TRIGGER IF EXISTS loading_chart_item_trigger ON loading_chart_items;
CREATE TRIGGER loading_chart_item_trigger
  AFTER INSERT ON loading_chart_items
  FOR EACH ROW
  EXECUTE FUNCTION process_parcel_loading();

-- Step 6: Create trigger for receiving with proper quantity-based logic
-- Note: This trigger will be activated when loading_chart_items status is updated to 'Received'
CREATE OR REPLACE FUNCTION handle_parcel_receiving()
RETURNS TRIGGER AS $$
DECLARE
  v_parcel_id INT;
  v_parcel_delivery_branch_id INT;
  v_parcel_total_items INT;
  v_current_branch_id INT;
  v_total_received_quantity INT;
  v_new_status parcel_status;
  v_chart_number VARCHAR(50);
BEGIN
  -- Only process when status changes to 'Received'
  IF NEW.status = 'Received' AND OLD.status != 'Received' THEN

    -- Get parcel information including total items
    SELECT p.parcel_id, p.delivery_branch_id, p.number_of_items
    INTO v_parcel_id, v_parcel_delivery_branch_id, v_parcel_total_items
    FROM parcels p
    WHERE p.lr_number = NEW.lr_number;

    -- Get chart information and current branch
    SELECT lc.chart_number,
           (SELECT branch_id FROM users WHERE auth_id = lc.created_by)
    INTO v_chart_number, v_current_branch_id
    FROM loading_charts lc
    WHERE lc.chart_id = NEW.chart_id;

    -- Calculate total received quantity for this parcel across all loading charts
    SELECT COALESCE(SUM(lci.quantity), 0)
    INTO v_total_received_quantity
    FROM loading_chart_items lci
    WHERE lci.lr_number = NEW.lr_number
    AND lci.status = 'Received';

    -- Determine new status based on quantity and destination
    IF v_total_received_quantity >= v_parcel_total_items THEN
      -- All items have been received - always set to 'Received' regardless of branch
      -- 'Delivered' status can only be set through manual delivery action
      v_new_status := 'Received';

      -- Update parcel status only when all items are received
      UPDATE parcels
      SET current_status = v_new_status
      WHERE parcel_id = v_parcel_id;

    ELSE
      -- Partial items received - keep status as 'Loaded'
      -- Don't update parcel status, but still create action record
      v_new_status := 'Received'; -- This would be the status when all items are received
    END IF;

    -- Always create parcel action record for receiving activity
    INSERT INTO parcel_actions (
      parcel_id,
      action_type,
      action_timestamp,
      branch_id,
      location_name,
      vehicle_id,
      quantity_received,
      remarks,
      reference_number
    )
    SELECT
      v_parcel_id,
      'Received'::VARCHAR(50), -- Always log as 'Received' action regardless of final status
      NOW(),
      v_current_branch_id,
      (SELECT name FROM branches WHERE branch_id = v_current_branch_id),
      lc.vehicle_id,
      NEW.quantity,
      CASE
        WHEN v_total_received_quantity >= v_parcel_total_items THEN
          CASE
            WHEN v_parcel_delivery_branch_id = v_current_branch_id THEN 'All items received at destination branch'
            ELSE 'All items received at intermediate branch'
          END
        ELSE
          'Partial items received (' || v_total_received_quantity || '/' || v_parcel_total_items || ')'
      END,
      v_chart_number
    FROM loading_charts lc
    WHERE lc.chart_id = NEW.chart_id;

    -- Note: Removed automatic 'Delivered' action creation
    -- 'Delivered' status can only be set through manual delivery action

  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER loading_chart_item_receiving_trigger
  AFTER UPDATE OF status ON loading_chart_items
  FOR EACH ROW
  EXECUTE FUNCTION handle_parcel_receiving();

-- Step 7: Add comments for documentation
COMMENT ON COLUMN loading_charts.loading_type IS 'Type of loading: Direct (final destination) or Via (intermediate stop)';
COMMENT ON COLUMN loading_charts.destination_city_id IS 'City where parcels are being loaded to';
COMMENT ON COLUMN loading_chart_items.loading_type IS 'Inherited from loading chart - Direct or Via';
COMMENT ON COLUMN loading_chart_items.destination_branch_id IS 'Specific branch where parcels are being loaded to';
