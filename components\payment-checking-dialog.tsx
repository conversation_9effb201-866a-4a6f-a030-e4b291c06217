"use client"

import { useState } from "react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { MockPaymentQR } from "@/components/mock-payment-qr"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface PaymentCheckingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookingId: string
  amount: string
  onSuccess: () => void
}

export function PaymentCheckingDialog({
  open,
  onOpenChange,
  bookingId,
  amount = "0",
  onSuccess
}: PaymentCheckingDialogProps) {
  const [status, setStatus] = useState<"payment" | "checking" | "success" | "error">("payment")
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleCancel = () => {
    setShowCancelConfirm(true)
  }

  const confirmCancel = () => {
    onOpenChange(false)
    setStatus("payment")
    setShowCancelConfirm(false)

    toast({
      title: "Payment Cancelled",
      description: "You have cancelled the payment process.",
      variant: "destructive"
    })
  }

  const handlePaymentComplete = async (paymentMethod: string) => {
    setStatus("checking")

    try {
      // Get user's branch ID from session
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        console.error("No session found when processing payment")
        setStatus("error")
        return
      }

      // Get user details to find branch ID
      const { data: userData } = await supabase
        .from('users')
        .select('branch_id')
        .eq('auth_id', session.user.id)
        .single()

      if (!userData || !userData.branch_id) {
        console.error("Could not determine branch ID for payment")
        setStatus("error")
        return
      }

      // Import the payment helper function
      const { createParcelPaymentTransaction } = await import('@/lib/payment-helpers')

      // Create a financial transaction for this payment
      const transaction = await createParcelPaymentTransaction(
        userData.branch_id,
        parseFloat(amount),
        paymentMethod,
        bookingId,
        `Payment for booking ${bookingId} via ${paymentMethod}`
      )

      if (!transaction) {
        console.error("Failed to create payment transaction")
        setStatus("error")
        return
      }

      // Payment successful
      setStatus("success")
    } catch (error) {
      console.error("Error processing payment:", error)
      setStatus("error")
    }
  }

  const handleContinue = () => {
    if (status === "success") {
      onSuccess()
      onOpenChange(false)
      setStatus("payment")
    }
  }

  return (
    <>
      <AlertDialog open={open && !showCancelConfirm} onOpenChange={onOpenChange}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex flex-col items-center justify-center text-center">
              {status === "payment" && (
                <>
                  <AlertDialogTitle className="text-xl font-semibold">Make Payment</AlertDialogTitle>
                  <AlertDialogDescription className="mt-2">
                    Complete your payment for booking #{bookingId}
                  </AlertDialogDescription>
                </>
              )}

              {status === "checking" && (
                <>
                  <Loader2 className="h-16 w-16 animate-spin text-primary mb-4" />
                  <AlertDialogTitle className="text-xl font-semibold">Checking Payment</AlertDialogTitle>
                  <AlertDialogDescription className="mt-2">
                    Please wait while we verify your payment for booking #{bookingId}. This may take a few moments.
                  </AlertDialogDescription>
                </>
              )}

              {status === "success" && (
                <>
                  <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
                  <AlertDialogTitle className="text-xl font-semibold">Payment Verified!</AlertDialogTitle>
                  <AlertDialogDescription className="mt-2">
                    Your payment has been successfully verified. You can now proceed to complete your booking.
                  </AlertDialogDescription>
                </>
              )}

              {status === "error" && (
                <>
                  <AlertCircle className="h-16 w-16 text-destructive mb-4" />
                  <AlertDialogTitle className="text-xl font-semibold">Payment Verification Failed</AlertDialogTitle>
                  <AlertDialogDescription className="mt-2">
                    We couldn't verify your payment. Please try again or contact customer support for assistance.
                  </AlertDialogDescription>
                </>
              )}
            </div>
          </AlertDialogHeader>

          {status === "payment" && (
            <div className="w-full flex justify-center">
              <div className="w-full max-w-md">
                <Tabs defaultValue="upi" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="upi">UPI</TabsTrigger>
                    <TabsTrigger value="cash">Cash</TabsTrigger>
                  </TabsList>

                  <TabsContent value="upi" className="mt-4 flex justify-center">
                    <MockPaymentQR
                      amount={amount}
                      reference={bookingId}
                      onPaymentComplete={() => handlePaymentComplete('upi')}
                    />
                  </TabsContent>

                  <TabsContent value="cash" className="mt-4 flex justify-center">
                    <div className="text-center p-6">
                      <p className="mb-4">Cash payment can be collected at the counter.</p>
                      <Button onClick={() => handlePaymentComplete('cash')}>
                        Mark as Paid (Cash)
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>

          )}

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-2">
            {status === "payment" && (
              <Button
                variant="outline"
                onClick={handleCancel}
                className="w-full sm:w-auto"
              >
                Cancel Payment
              </Button>
            )}

            {status === "checking" && (
              <Button
                variant="outline"
                onClick={handleCancel}
                className="w-full sm:w-auto"
              >
                Cancel Checking
              </Button>
            )}

            {status === "success" && (
              <Button
                onClick={handleContinue}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
              >
                Continue
              </Button>
            )}

            {status === "error" && (
              <>
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => setStatus("payment")}
                  className="w-full sm:w-auto bg-primary hover:bg-primary/90"
                >
                  Try Again
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Confirmation dialog for canceling payment */}
      <AlertDialog open={showCancelConfirm} onOpenChange={setShowCancelConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Payment?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel the payment process? This will not complete your booking.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCancelConfirm(false)}>
              Continue Payment
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel} className="bg-destructive hover:bg-destructive/90">
              Yes, Cancel
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
