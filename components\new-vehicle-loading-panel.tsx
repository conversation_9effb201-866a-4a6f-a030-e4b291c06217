"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Minus, Trash2, Truck, MapPin, Package } from "lucide-react"
import { LoadingChartCompletionDialog } from "@/components/loading-chart-completion-dialog"

interface City {
  city_id: number
  name: string
  state: string
}

interface Branch {
  branch_id: number
  name: string
  code: string
  city_id: number
}

interface Vehicle {
  vehicle_id: number
  registration_number: string
  vehicle_type: string
  current_status: string
}

interface LREntry {
  lrNumber: string
  quantity: number
  isValid: boolean
  validationMessage?: string
  destination?: string
  parcelDestinationBranchId?: number
}

export function NewVehicleLoadingPanel() {
  const { toast } = useToast()

  // Step management
  const [step, setStep] = useState<"vehicle" | "destination" | "lr" | "confirmation" | "complete">("vehicle")
  const [showCompletionDialog, setShowCompletionDialog] = useState(false)
  const [completionData, setCompletionData] = useState<any>(null)

  // Vehicle selection
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [isSearchingVehicle, setIsSearchingVehicle] = useState(false)
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null)

  // Destination selection
  const [cities, setCities] = useState<City[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [selectedCityId, setSelectedCityId] = useState<string>("")
  const [selectedBranchId, setSelectedBranchId] = useState<string>("")
  const [loadingType, setLoadingType] = useState<"Direct" | "Via">("Direct")

  // LR management
  const [lrEntries, setLrEntries] = useState<LREntry[]>([])
  const [currentLR, setCurrentLR] = useState("")
  const [currentQuantity, setCurrentQuantity] = useState("1")
  const [isValidating, setIsValidating] = useState(false)
  const [suggestedLRs, setSuggestedLRs] = useState<any[]>([])

  // Submission
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [remarks, setRemarks] = useState("")

  // Load cities on component mount
  useEffect(() => {
    fetchCities()
  }, [])

  // Load branches when city is selected
  useEffect(() => {
    if (selectedCityId) {
      fetchBranches(parseInt(selectedCityId))
    } else {
      setBranches([])
      setSelectedBranchId("")
    }
  }, [selectedCityId])

  // Load suggested LRs when destination is selected
  useEffect(() => {
    if (selectedBranchId && loadingType) {
      fetchSuggestedLRs(parseInt(selectedBranchId), loadingType)
    }
  }, [selectedBranchId, loadingType])

  const fetchCities = async () => {
    try {
      console.log('Fetching cities...')
      const response = await fetch('/api/cities')
      const data = await response.json()
      console.log('Cities API response:', data)
      if (response.ok) {
        setCities(data.cities || [])
        console.log('Set cities:', data.cities?.length || 0, 'cities')
      } else {
        console.error('Error fetching cities:', data.error)
      }
    } catch (error) {
      console.error('Error fetching cities:', error)
    }
  }

  const fetchBranches = async (cityId: number) => {
    try {
      console.log('Fetching branches for city ID:', cityId)
      const response = await fetch(`/api/branches/by-city?city_id=${cityId}&status=Active`)
      const data = await response.json()
      console.log('Branches API response:', data)
      if (response.ok) {
        setBranches(data.branches || [])
        console.log('Set branches:', data.branches?.length || 0, 'branches')
      } else {
        console.error('Error fetching branches:', data.error)
        setBranches([])
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
      setBranches([])
    }
  }

  const fetchSuggestedLRs = async (destinationBranchId: number, type: string) => {
    try {
      const response = await fetch('/api/parcels/suggested-for-loading', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          destination_branch_id: destinationBranchId,
          loading_type: type
        })
      })
      const data = await response.json()
      if (response.ok) {
        setSuggestedLRs(data.parcels || [])
      }
    } catch (error) {
      console.error('Error fetching suggested LRs:', error)
    }
  }

  const searchVehicle = async () => {
    if (!vehicleNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearchingVehicle(true)
    setError(null)

    try {
      // First, search for the vehicle
      const vehicleResponse = await fetch(`/api/vehicles/search?registration_number=${encodeURIComponent(vehicleNumber.trim())}`)
      const vehicleData = await vehicleResponse.json()

      if (!vehicleResponse.ok || !vehicleData.vehicle) {
        setError(vehicleData.error || "Vehicle not found")
        return
      }

      if (vehicleData.vehicle.current_status !== "Active") {
        setError(`Vehicle status is "${vehicleData.vehicle.current_status}". Only active vehicles can be used for loading.`)
        return
      }

      // Check for active memos
      const memoResponse = await fetch(`/api/vehicles/memo-check?vehicle_number=${encodeURIComponent(vehicleNumber.trim())}`)
      const memoData = await memoResponse.json()

      if (memoResponse.ok) {
        if (memoData.has_active_memo) {
          if (memoData.memo_from_user_branch) {
            // Vehicle has active memo from user's branch - show confirmation
            toast({
              title: "Active Memo Found",
              description: `Vehicle has active memo: ${memoData.memo.memo_number}`,
            })
          } else {
            // Vehicle has active memo from another branch - show warning but allow
            toast({
              title: "Memo from Another Branch",
              description: memoData.message,
              variant: "destructive",
            })
          }
        } else {
          // No active memo found
          toast({
            title: "No Active Memo",
            description: "No memo found for this vehicle. You can proceed with loading.",
          })
        }
      }

      // Proceed with vehicle selection regardless of memo status
      setSelectedVehicle(vehicleData.vehicle)
      setStep("destination")

    } catch (error: any) {
      console.error('Error searching vehicle:', error)
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearchingVehicle(false)
    }
  }

  const validateLR = async (lrNumber: string): Promise<LREntry> => {
    setIsValidating(true)

    try {
      const params = new URLSearchParams({
        lr_number: lrNumber,
        destination_branch_id: selectedBranchId,
        loading_type: loadingType
      })

      const response = await fetch(`/api/parcels/validate-lr-for-loading?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: true,
          destination: data.parcel.delivery_branch?.name || 'Unknown',
          parcelDestinationBranchId: data.parcel.delivery_branch_id
        }
      } else {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: false,
          validationMessage: data.message || 'Invalid LR number'
        }
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      return {
        lrNumber,
        quantity: parseInt(currentQuantity) || 1,
        isValid: false,
        validationMessage: 'Failed to validate LR number'
      }
    } finally {
      setIsValidating(false)
    }
  }

  const addLREntry = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    const lrNumber = currentLR.trim()
    const quantity = parseInt(currentQuantity) || 1

    // Check if LR already exists in the list - if so, merge quantities
    const existingEntryIndex = lrEntries.findIndex(entry => entry.lrNumber === lrNumber)

    if (existingEntryIndex !== -1) {
      // Validate the LR first to ensure it's still valid
      const validatedEntry = await validateLR(lrNumber)

      if (validatedEntry.isValid) {
        // Merge quantities
        setLrEntries(prev => prev.map((entry, index) =>
          index === existingEntryIndex
            ? { ...entry, quantity: entry.quantity + quantity }
            : entry
        ))

        // Notification removed - quantity update is done silently
      } else {
        toast({
          title: "Validation Failed",
          description: validatedEntry.validationMessage || "LR number is no longer valid",
          variant: "destructive",
        })
      }
    } else {
      // New LR entry
      const validatedEntry = await validateLR(lrNumber)
      setLrEntries(prev => [...prev, validatedEntry])

      // Notification removed - LR entry is added silently
    }

    setCurrentLR("")
    setCurrentQuantity("1")
  }

  const removeLREntry = (index: number) => {
    setLrEntries(prev => prev.filter((_, i) => i !== index))
  }

  const adjustQuantity = (index: number, change: number) => {
    setLrEntries(prev => prev.map((entry, i) => {
      if (i === index) {
        const newQuantity = Math.max(1, entry.quantity + change)
        return { ...entry, quantity: newQuantity }
      }
      return entry
    }))
  }

  const proceedToConfirmation = () => {
    if (lrEntries.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please add at least one LR number",
        variant: "destructive",
      })
      return
    }

    const invalidLRs = lrEntries.filter(entry => !entry.isValid)
    if (invalidLRs.length > 0) {
      toast({
        title: "Invalid LR Numbers",
        description: `There are ${invalidLRs.length} invalid LR numbers in the list`,
        variant: "destructive",
      })
      return
    }

    setStep("confirmation")
  }

  const generateLoadingChart = async () => {
    if (!selectedVehicle || !selectedBranchId) {
      setError("Missing vehicle or destination information")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/loading-charts/new', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          vehicle_id: selectedVehicle.vehicle_id,
          destination_branch_id: parseInt(selectedBranchId),
          destination_city_id: parseInt(selectedCityId),
          loading_type: loadingType,
          remarks: remarks.trim() || undefined,
          lr_entries: lrEntries.map(entry => ({
            lr_number: entry.lrNumber,
            quantity: entry.quantity
          }))
        }),
      })

      const data = await response.json()

      if (response.ok) {
        // Prepare completion data
        const selectedCity = cities.find(c => c.city_id.toString() === selectedCityId)
        const selectedBranch = branches.find(b => b.branch_id.toString() === selectedBranchId)

        const completionInfo = {
          chart_id: data.chart_id,
          chart_number: data.chart_number,
          vehicle: {
            registration_number: selectedVehicle.registration_number,
            vehicle_type: selectedVehicle.vehicle_type
          },
          destination: {
            branch_name: selectedBranch?.name || 'Unknown',
            city_name: selectedCity?.name || 'Unknown'
          },
          loading_type: loadingType,
          parcels: lrEntries.map(entry => ({
            lr_number: entry.lrNumber,
            quantity: entry.quantity,
            destination: entry.destination || 'Unknown'
          })),
          total_parcels: lrEntries.length,
          created_at: new Date().toISOString()
        }

        setCompletionData(completionInfo)
        setShowCompletionDialog(true)
        setStep("complete")
      } else {
        setError(data.error || "Failed to create loading chart")
      }
    } catch (error: any) {
      console.error('Error creating loading chart:', error)
      setError('Failed to create loading chart. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setStep("vehicle")
    setVehicleNumber("")
    setSelectedVehicle(null)
    setSelectedCityId("")
    setSelectedBranchId("")
    setLoadingType("Direct")
    setLrEntries([])
    setCurrentLR("")
    setCurrentQuantity("1")
    setSuggestedLRs([])
    setError(null)
    setRemarks("")
    setShowCompletionDialog(false)
    setCompletionData(null)
  }

  const handleCompletionClose = () => {
    setShowCompletionDialog(false)
    resetForm()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Truck className="h-5 w-5" />
        <h2 className="text-lg font-semibold">Vehicle Loading</h2>
      </div>

      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Step 1: Vehicle Selection */}
      {step === "vehicle" && (
        <Card>
          <CardHeader>
            <CardTitle>Step 1: Select Vehicle</CardTitle>
            <CardDescription>Enter the vehicle registration number to start loading</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle-number">Vehicle Registration Number</Label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <Input
                  id="vehicle-number"
                  placeholder="Enter vehicle number (e.g., KA01AB1234)"
                  value={vehicleNumber}
                  onChange={(e) => setVehicleNumber(e.target.value.toUpperCase())}
                  onKeyPress={(e) => e.key === 'Enter' && searchVehicle()}
                  className="flex-1"
                />
                <Button
                  onClick={searchVehicle}
                  disabled={isSearchingVehicle}
                  className="w-full sm:w-auto"
                >
                  {isSearchingVehicle ? <Loader2 className="h-4 w-4 animate-spin" /> : "Search"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: Destination Selection */}
      {step === "destination" && selectedVehicle && (
        <Card>
          <CardHeader>
            <CardTitle>Step 2: Select Destination</CardTitle>
            <CardDescription>Choose the city and branch where parcels will be loaded to</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <div className="flex items-center space-x-2">
                <Truck className="h-4 w-4" />
                <span className="font-medium">{selectedVehicle.registration_number}</span>
                <Badge variant="outline">{selectedVehicle.vehicle_type}</Badge>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Destination City</Label>
                <Select value={selectedCityId} onValueChange={setSelectedCityId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={city.city_id} value={city.city_id.toString()}>
                        {city.name}, {city.state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>To Branch</Label>
                <Select
                  value={selectedBranchId}
                  onValueChange={setSelectedBranchId}
                  disabled={!selectedCityId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.branch_id} value={branch.branch_id.toString()}>
                        {branch.name} ({branch.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Loading Type</Label>
              <Tabs value={loadingType} onValueChange={(value) => setLoadingType(value as "Direct" | "Via")}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="Direct">Direct Loading</TabsTrigger>
                  <TabsTrigger value="Via">Via Loading</TabsTrigger>
                </TabsList>
                <TabsContent value="Direct" className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    Load parcels whose final destination matches the selected branch
                  </p>
                </TabsContent>
                <TabsContent value="Via" className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    Load parcels that will pass through the selected branch (intermediate stop)
                  </p>
                </TabsContent>
              </Tabs>
            </div>

            <Button
              onClick={() => setStep("lr")}
              disabled={!selectedBranchId}
              className="w-full"
            >
              Continue to Add Parcels
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Step 3: LR Entry */}
      {step === "lr" && (
        <Card>
          <CardHeader>
            <CardTitle>Step 3: Add Parcels</CardTitle>
            <CardDescription>
              Add LR numbers for {loadingType.toLowerCase()} loading to {branches.find(b => b.branch_id.toString() === selectedBranchId)?.name}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span className="font-medium">
                    {cities.find(c => c.city_id.toString() === selectedCityId)?.name} → {branches.find(b => b.branch_id.toString() === selectedBranchId)?.name}
                  </span>
                  <Badge variant={loadingType === "Direct" ? "default" : "secondary"}>
                    {loadingType}
                  </Badge>
                </div>
                <Badge variant="outline">{selectedVehicle?.registration_number}</Badge>
              </div>
            </div>

            {/* LR Input */}
            <div className="grid grid-cols-1 sm:grid-cols-4 gap-2">
              <div className="sm:col-span-3">
                <Label htmlFor="lr-number">LR Number</Label>
                <Input
                  id="lr-number"
                  placeholder="Enter LR number"
                  value={currentLR}
                  onChange={(e) => setCurrentLR(e.target.value.toUpperCase())}
                  onKeyPress={(e) => e.key === 'Enter' && addLREntry()}
                />
              </div>
              <div>
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={currentQuantity}
                  onChange={(e) => setCurrentQuantity(e.target.value)}
                />
              </div>
            </div>

            <Button
              onClick={addLREntry}
              disabled={isValidating || !currentLR.trim()}
              className="w-full"
            >
              {isValidating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
              Add LR Number
            </Button>

            {/* Suggested LRs */}
            {suggestedLRs.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Suggested LRs for {loadingType} Loading</Label>
                  <Badge variant="outline">{suggestedLRs.length} available</Badge>
                </div>
                <ScrollArea className="h-[120px] rounded-md border">
                  <div className="p-4 space-y-2">
                    {suggestedLRs.map((parcel) => (
                      <div
                        key={parcel.lr_number}
                        className="flex items-center justify-between p-2 border rounded-md cursor-pointer hover:bg-muted"
                        onClick={() => {
                          setCurrentLR(parcel.lr_number);
                          setCurrentQuantity(parcel.number_of_items?.toString() || "1");
                        }}
                      >
                        <div>
                          <p className="font-medium">{parcel.lr_number}</p>
                          <p className="text-xs text-muted-foreground">
                            {parcel.sender_name} → {parcel.recipient_name}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {parcel.number_of_items} items
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* LR Entries List */}
            <div className="space-y-2">
              <Label>Added LR Numbers ({lrEntries.length})</Label>
              <ScrollArea className="h-[200px] rounded-md border">
                <div className="p-4 space-y-2">
                  {lrEntries.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No LR numbers added yet
                    </div>
                  ) : (
                    lrEntries.map((entry, index) => (
                      <Card key={index} className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{entry.lrNumber}</span>
                              <Badge variant={entry.isValid ? "default" : "destructive"}>
                                {entry.isValid ? "Valid" : "Invalid"}
                              </Badge>
                              <Badge variant="outline">Qty: {entry.quantity}</Badge>
                            </div>
                            {entry.isValid ? (
                              <p className="text-xs text-muted-foreground mt-1">
                                Destination: {entry.destination}
                              </p>
                            ) : (
                              <p className="text-xs text-destructive mt-1">
                                {entry.validationMessage}
                              </p>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeLREntry(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setStep("destination")} className="flex-1">
                Back
              </Button>
              <Button onClick={proceedToConfirmation} className="flex-1">
                Continue to Confirmation
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Confirmation */}
      {step === "confirmation" && (
        <Card>
          <CardHeader>
            <CardTitle>Step 4: Confirm Loading Chart</CardTitle>
            <CardDescription>Review the details before creating the loading chart</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Vehicle</Label>
                <div className="p-3 bg-muted rounded-md">
                  <div className="flex items-center space-x-2">
                    <Truck className="h-4 w-4" />
                    <span className="font-medium">{selectedVehicle?.registration_number}</span>
                    <Badge variant="outline">{selectedVehicle?.vehicle_type}</Badge>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Destination</Label>
                <div className="p-3 bg-muted rounded-md">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span className="font-medium">
                      {branches.find(b => b.branch_id.toString() === selectedBranchId)?.name}
                    </span>
                    <Badge variant={loadingType === "Direct" ? "default" : "secondary"}>
                      {loadingType}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>LR Numbers to Load ({lrEntries.length})</Label>
              <ScrollArea className="h-[200px] rounded-md border">
                <div className="p-4 space-y-2">
                  {lrEntries.map((entry, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b last:border-0">
                      <div className="flex-1">
                        <p className="font-medium">{entry.lrNumber}</p>
                        <p className="text-xs text-muted-foreground">
                          Destination: {entry.destination}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1 bg-muted rounded-md p-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => adjustQuantity(index, -1)}
                            disabled={entry.quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-sm font-medium min-w-[2rem] text-center">
                            {entry.quantity}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => adjustQuantity(index, 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          onClick={() => removeLREntry(index)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Remarks Input */}
            {/* <div className="space-y-2">
              <Label htmlFor="loading-remarks">Loading Remarks (Optional)</Label>
              <Input
                id="loading-remarks"
                placeholder="Enter any remarks for this loading operation..."
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
              />
            </div> */}

            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setStep("lr")} className="flex-1">
                Back
              </Button>
              <Button
                onClick={generateLoadingChart}
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Package className="h-4 w-4 mr-2" />}
                Create Loading Chart
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 5: Complete */}
      {step === "complete" && (
        <Card>
          <CardHeader>
            <CardTitle>Loading Chart Created Successfully!</CardTitle>
            <CardDescription>The loading chart has been created and parcels have been marked as loaded</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-green-600" />
              <p className="text-lg font-medium">Loading chart created successfully</p>
              <p className="text-muted-foreground">
                {lrEntries.length} parcels have been loaded for {loadingType.toLowerCase()} delivery
              </p>
            </div>
            <Button onClick={resetForm} className="w-full">
              Create Another Loading Chart
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Completion Dialog */}
      {completionData && (
        <LoadingChartCompletionDialog
          open={showCompletionDialog}
          onOpenChange={setShowCompletionDialog}
          data={completionData}
          onClose={handleCompletionClose}
        />
      )}
    </div>
  )
}
