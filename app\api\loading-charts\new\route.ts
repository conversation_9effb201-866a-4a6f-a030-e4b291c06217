import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// Generate loading chart number
function generateLoadingChartNumber(): string {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0");
  return `LC${year}${month}${day}${random}`;
}

// POST /api/loading-charts/new - Create new loading chart with destination selection
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (
      !body.vehicle_id || !body.destination_branch_id || !body.loading_type ||
      !body.lr_entries || body.lr_entries.length === 0
    ) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: vehicle_id, destination_branch_id, loading_type, lr_entries",
        },
        { status: 400 },
      );
    }

    // Validate loading type
    if (!["Direct", "Via"].includes(body.loading_type)) {
      return NextResponse.json(
        { error: "Invalid loading_type. Must be 'Direct' or 'Via'" },
        { status: 400 },
      );
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Generate loading chart number
    const chart_number = generateLoadingChartNumber();

    // Get user's branch for validation
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Validate that all LR numbers are eligible for the specified loading type
    const lrNumbers = body.lr_entries.map((entry: any) => entry.lr_number);

    const { data: parcels, error: parcelsError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        delivery_branch_id,
        current_status,
        sender_branch_id,
        number_of_items,
        delivery_branch:branches!delivery_branch_id(name)
      `)
      .in("lr_number", lrNumbers)
      .in("current_status", ["Booked", "Received"]);

    if (parcelsError) {
      console.error("Error validating parcels:", parcelsError);
      return NextResponse.json({ error: "Failed to validate parcels" }, {
        status: 500,
      });
    }

    if (!parcels || parcels.length !== lrNumbers.length) {
      const foundLRs = parcels?.map((p) => p.lr_number) || [];
      const missingLRs = lrNumbers.filter((lr: string) =>
        !foundLRs.includes(lr)
      );
      return NextResponse.json(
        {
          error: `Invalid LR numbers or parcels not eligible for loading: ${
            missingLRs.join(", ")
          }`,
        },
        { status: 400 },
      );
    }

    // Validate parcel locations and quantities
    const quantityMismatches = [];
    const locationErrors = [];

    for (const parcel of parcels) {
      // Check current location
      let currentLocation = parcel.sender_branch_id; // Default for 'Booked' parcels

      if (parcel.current_status === "Received") {
        // For received parcels, check the latest action to find current location
        const { data: latestAction, error: actionError } = await supabase
          .from("parcel_actions")
          .select("branch_id")
          .eq("parcel_id", parcel.parcel_id)
          .eq("action_type", "Received")
          .order("created_at", { ascending: false })
          .limit(1)
          .single();

        if (!actionError && latestAction) {
          currentLocation = latestAction.branch_id;
        }
      }

      // Check if parcel is at user's branch
      if (currentLocation !== userData.branch_id) {
        const locationMessage = parcel.current_status === "Booked"
          ? `LR ${parcel.lr_number} is not at your branch (booked at different branch)`
          : `LR ${parcel.lr_number} is not at your branch (currently at another location)`;
        locationErrors.push(locationMessage);
      }

      // Check quantity mismatch
      const lrEntry = body.lr_entries.find((entry: any) =>
        entry.lr_number === parcel.lr_number
      );
      if (
        lrEntry && parcel.number_of_items &&
        lrEntry.quantity !== parcel.number_of_items
      ) {
        quantityMismatches.push(
          `LR ${parcel.lr_number} has mismatching item count`,
        );
      }

      // Validate loading type logic
      const isDirectLoading =
        parcel.delivery_branch_id === body.destination_branch_id;

      if (body.loading_type === "Direct" && !isDirectLoading) {
        return NextResponse.json(
          {
            error:
              `LR ${parcel.lr_number} cannot be loaded as Direct - its destination (${parcel.delivery_branch?.name}) does not match the loading destination`,
          },
          { status: 400 },
        );
      }

      if (body.loading_type === "Via" && isDirectLoading) {
        return NextResponse.json(
          {
            error:
              `LR ${parcel.lr_number} cannot be loaded as Via - its destination (${parcel.delivery_branch?.name}) matches the loading destination`,
          },
          { status: 400 },
        );
      }
    }

    // Return errors if any validation failed
    if (locationErrors.length > 0) {
      return NextResponse.json(
        { error: locationErrors.join("; ") },
        { status: 403 },
      );
    }

    if (quantityMismatches.length > 0) {
      return NextResponse.json(
        { error: quantityMismatches.join("; ") },
        { status: 400 },
      );
    }

    // Create loading chart
    const { data: chart, error: chartError } = await supabase
      .from("loading_charts")
      .insert({
        chart_number,
        vehicle_id: body.vehicle_id,
        destination_branch_id: body.destination_branch_id,
        destination_city_id: body.destination_city_id || null,
        loading_type: body.loading_type,
        created_by: user?.id || null,
        status: "Created",
        total_parcels: body.lr_entries.length,
        loaded_parcels: 0,
        notes: body.remarks || `${body.loading_type} loading to destination`,
      })
      .select()
      .single();

    if (chartError) {
      console.error("Error creating loading chart:", chartError);
      return NextResponse.json({ error: "Failed to create loading chart" }, {
        status: 500,
      });
    }

    // Create loading chart items
    const chartItems = body.lr_entries.map((entry: any) => ({
      chart_id: chart.chart_id,
      lr_number: entry.lr_number,
      quantity: entry.quantity,
      status: "Pending",
      loading_type: body.loading_type,
      destination_branch_id: body.destination_branch_id,
    }));

    console.log("Creating loading chart items:", chartItems);

    const { error: itemsError } = await supabase
      .from("loading_chart_items")
      .insert(chartItems);

    if (itemsError) {
      console.error("Error creating loading chart items:", itemsError);
      console.error("Chart items data:", chartItems);
      console.error(
        "Supabase error details:",
        JSON.stringify(itemsError, null, 2),
      );

      // Rollback: delete the chart
      await supabase
        .from("loading_charts")
        .delete()
        .eq("chart_id", chart.chart_id);

      return NextResponse.json({
        error: `Failed to create loading chart items: ${
          itemsError.message || itemsError.details || "Unknown error"
        }`,
      }, { status: 500 });
    }

    // The database triggers will handle:
    // 1. Updating parcel status to 'Loaded'
    // 2. Creating parcel_actions records
    // 3. Updating loading chart loaded_parcels count

    return NextResponse.json({
      message: "Loading chart created successfully",
      chart_id: chart.chart_id,
      chart_number: chart.chart_number,
      loading_type: body.loading_type,
      total_parcels: body.lr_entries.length,
    });
  } catch (error: any) {
    console.error("Error in POST /api/loading-charts/new:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
