import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/validate-lr-for-loading
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number");
    const destination_branch_id = url.searchParams.get("destination_branch_id");
    const loading_type = url.searchParams.get("loading_type");

    // Validate required parameters
    if (!lr_number || !destination_branch_id || !loading_type) {
      return NextResponse.json(
        {
          valid: false,
          message:
            "Missing required parameters: lr_number, destination_branch_id, loading_type",
        },
        { status: 400 },
      );
    }

    // Validate loading type
    if (!["Direct", "Via"].includes(loading_type)) {
      return NextResponse.json(
        {
          valid: false,
          message: "Invalid loading_type. Must be 'Direct' or 'Via'",
        },
        { status: 400 },
      );
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json(
        {
          valid: false,
          message: "User not found",
        },
        { status: 404 },
      );
    }

    // Get parcel details
    const { data: parcel, error: parcelError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        sender_branch_id,
        delivery_branch_id,
        number_of_items,
        current_status,
        delivery_branch:branches!delivery_branch_id(name, code),
        sender_branch:branches!sender_branch_id(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    if (parcelError || !parcel) {
      return NextResponse.json(
        {
          valid: false,
          message: "Parcel not found",
        },
        { status: 404 },
      );
    }

    // Validate parcel eligibility

    // 1. Check if parcel is currently at user's branch
    // For 'Booked' parcels, they should be at the sender branch
    // For 'Received' parcels, they should be at the current location
    let currentLocation = parcel.sender_branch_id; // Default for 'Booked' parcels

    if (parcel.current_status === "Received") {
      // For received parcels, check the latest action to find current location
      const { data: latestAction, error: actionError } = await supabase
        .from("parcel_actions")
        .select("branch_id")
        .eq("parcel_id", parcel.parcel_id)
        .eq("action_type", "Received")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (!actionError && latestAction) {
        currentLocation = latestAction.branch_id;
      }
    }

    if (currentLocation !== userData.branch_id) {
      const locationMessage = parcel.current_status === "Booked"
        ? `Parcel is not at your branch. It was booked at ${parcel.sender_branch?.name}`
        : `Parcel is not at your branch. It is currently at another location`;

      return NextResponse.json(
        {
          valid: false,
          message: locationMessage,
        },
        { status: 403 },
      );
    }

    // 2. Check if parcel status allows loading
    if (!["Booked", "Received"].includes(parcel.current_status)) {
      return NextResponse.json(
        {
          valid: false,
          message:
            `Parcel status is '${parcel.current_status}'. Only 'Booked' or 'Received' parcels can be loaded`,
        },
        { status: 400 },
      );
    }

    // 3. Check if parcel is already in a pending loading chart
    const { data: existingItem, error: existingError } = await supabase
      .from("loading_chart_items")
      .select("item_id, chart_id")
      .eq("lr_number", lr_number)
      .eq("status", "Pending")
      .maybeSingle();

    if (existingError) {
      console.error("Error checking existing loading items:", existingError);
      return NextResponse.json(
        {
          valid: false,
          message: "Failed to check if parcel is already loaded",
        },
        { status: 500 },
      );
    }

    if (existingItem) {
      return NextResponse.json(
        {
          valid: false,
          message: "Parcel is already in a pending loading chart",
        },
        { status: 400 },
      );
    }

    // 4. Validate loading type logic
    const destinationBranchId = parseInt(destination_branch_id);
    const isDirectLoading = parcel.delivery_branch_id === destinationBranchId;

    if (loading_type === "Direct" && !isDirectLoading) {
      return NextResponse.json(
        {
          valid: false,
          message:
            `Cannot load as Direct - parcel destination (${parcel.delivery_branch?.name}) does not match loading destination`,
        },
        { status: 400 },
      );
    }

    if (loading_type === "Via" && isDirectLoading) {
      return NextResponse.json(
        {
          valid: false,
          message:
            `Cannot load as Via - parcel destination (${parcel.delivery_branch?.name}) matches loading destination`,
        },
        { status: 400 },
      );
    }

    // All validations passed
    return NextResponse.json({
      valid: true,
      parcel: {
        ...parcel,
        loading_type: loading_type,
        is_direct_loading: isDirectLoading,
      },
    });
  } catch (error: any) {
    console.error("Error in GET /api/parcels/validate-lr-for-loading:", error);
    return NextResponse.json(
      {
        valid: false,
        message: "Internal server error",
      },
      { status: 500 },
    );
  }
}
