-- Delivery System Migration
-- This migration adds proper delivery functionality with manual delivery actions

-- Step 1: Create function for manual delivery action
CREATE OR REPLACE FUNCTION process_parcel_delivery(
  p_parcel_id INT,
  p_delivery_branch_id INT,
  p_recipient_name VARCHAR(255),
  p_recipient_id_type VARCHAR(50),
  p_recipient_id_number VARCHAR(100),
  p_delivery_notes TEXT,
  p_proof_url VARCHAR(255),
  p_delivered_by UUID
)
RETURNS JSON AS $$
DECLARE
  v_parcel_record RECORD;
  v_user_branch_id INT;
  v_result JSON;
BEGIN
  -- Get parcel information
  SELECT p.parcel_id, p.lr_number, p.current_status, p.delivery_branch_id, p.number_of_items,
         p.recipient_name as original_recipient_name
  INTO v_parcel_record
  FROM parcels p
  WHERE p.parcel_id = p_parcel_id;
  
  -- Check if parcel exists
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Parcel not found'
    );
  END IF;
  
  -- Get user's branch
  SELECT branch_id INTO v_user_branch_id
  FROM users 
  WHERE auth_id = p_delivered_by;
  
  -- Validate delivery conditions
  
  -- 1. Check if parcel status is 'Received'
  IF v_parcel_record.current_status != 'Received' THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Parcel status must be "Received" to deliver. Current status: ' || v_parcel_record.current_status
    );
  END IF;
  
  -- 2. Check if parcel is at its destination branch
  IF v_parcel_record.delivery_branch_id != p_delivery_branch_id THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Parcel can only be delivered at its destination branch'
    );
  END IF;
  
  -- 3. Check if user is at the correct branch
  IF v_user_branch_id != p_delivery_branch_id THEN
    RETURN json_build_object(
      'success', false,
      'error', 'You can only deliver parcels at your own branch'
    );
  END IF;
  
  -- 4. Verify all items have been received
  -- Calculate total received quantity across all loading charts
  DECLARE
    v_total_received INT;
  BEGIN
    SELECT COALESCE(SUM(lci.quantity), 0)
    INTO v_total_received
    FROM loading_chart_items lci
    WHERE lci.lr_number = v_parcel_record.lr_number
    AND lci.status = 'Received';
    
    IF v_total_received < v_parcel_record.number_of_items THEN
      RETURN json_build_object(
        'success', false,
        'error', 'Cannot deliver parcel. Only ' || v_total_received || ' of ' || v_parcel_record.number_of_items || ' items have been received'
      );
    END IF;
  END;
  
  -- All validations passed - proceed with delivery
  
  -- Update parcel status to 'Delivered'
  UPDATE parcels 
  SET 
    current_status = 'Delivered',
    actual_delivery_date = CURRENT_DATE,
    proof_of_delivery_url = p_proof_url,
    collector_details = json_build_object(
      'name', p_recipient_name,
      'id_type', p_recipient_id_type,
      'id_number', p_recipient_id_number,
      'delivery_timestamp', NOW()
    )
  WHERE parcel_id = p_parcel_id;
  
  -- Create delivery action record
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    operator_id,
    quantity_received,
    remarks,
    reference_number,
    created_by
  )
  SELECT
    p_parcel_id,
    'Delivered',
    NOW(),
    p_delivery_branch_id,
    (SELECT name FROM branches WHERE branch_id = p_delivery_branch_id),
    (SELECT user_id FROM users WHERE auth_id = p_delivered_by),
    v_parcel_record.number_of_items,
    COALESCE(p_delivery_notes, 'Parcel delivered to recipient'),
    'DELIVERY-' || v_parcel_record.lr_number,
    p_delivered_by;
  
  -- Return success response
  RETURN json_build_object(
    'success', true,
    'message', 'Parcel delivered successfully',
    'parcel_id', p_parcel_id,
    'lr_number', v_parcel_record.lr_number,
    'delivered_to', p_recipient_name,
    'delivery_timestamp', NOW()
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Delivery failed: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql;

-- Step 2: Add delivery-related columns to parcels table if they don't exist
DO $$
BEGIN
  -- Add collector_details column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'parcels' AND column_name = 'collector_details'
  ) THEN
    ALTER TABLE parcels ADD COLUMN collector_details JSONB;
    COMMENT ON COLUMN parcels.collector_details IS 'JSON object containing details of the person who collected the parcel';
  END IF;
  
  -- Add recipient_id_details column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'parcels' AND column_name = 'recipient_id_details'
  ) THEN
    ALTER TABLE parcels ADD COLUMN recipient_id_details JSONB;
    COMMENT ON COLUMN parcels.recipient_id_details IS 'JSON object containing ID proof details of the recipient';
  END IF;
END$$;

-- Step 3: Create view for delivery-eligible parcels
CREATE OR REPLACE VIEW delivery_eligible_parcels AS
SELECT 
  p.parcel_id,
  p.lr_number,
  p.sender_name,
  p.recipient_name,
  p.recipient_phone,
  p.delivery_branch_id,
  p.number_of_items,
  p.current_status,
  p.booking_datetime,
  db.name as destination_branch_name,
  db.code as destination_branch_code,
  
  -- Calculate total received items
  COALESCE(received_summary.total_received, 0) as total_received_items,
  
  -- Check if all items are received
  CASE 
    WHEN COALESCE(received_summary.total_received, 0) >= p.number_of_items THEN true
    ELSE false
  END as all_items_received,
  
  -- Check if parcel is at destination and ready for delivery
  CASE 
    WHEN p.current_status = 'Received' 
    AND COALESCE(received_summary.total_received, 0) >= p.number_of_items THEN true
    ELSE false
  END as ready_for_delivery

FROM parcels p
LEFT JOIN branches db ON p.delivery_branch_id = db.branch_id
LEFT JOIN (
  SELECT 
    lci.lr_number,
    SUM(lci.quantity) as total_received
  FROM loading_chart_items lci
  WHERE lci.status = 'Received'
  GROUP BY lci.lr_number
) received_summary ON p.lr_number = received_summary.lr_number

WHERE p.current_status IN ('Received', 'Loaded')
ORDER BY p.booking_datetime DESC;

-- Step 4: Add comments for documentation
COMMENT ON FUNCTION process_parcel_delivery IS 'Handles manual delivery of parcels with proper validation and action tracking';
COMMENT ON VIEW delivery_eligible_parcels IS 'Shows parcels that are eligible for delivery with received item counts';

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS parcels_delivery_status_idx ON parcels(current_status, delivery_branch_id) 
WHERE current_status IN ('Received', 'Loaded');

CREATE INDEX IF NOT EXISTS parcel_actions_delivery_idx ON parcel_actions(action_type, action_timestamp) 
WHERE action_type = 'Delivered';
