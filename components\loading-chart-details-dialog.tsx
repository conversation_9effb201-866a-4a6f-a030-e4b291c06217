"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { X, Package, Truck, MapPin, Clock, User, Phone, FileText, AlertCircle } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { ParcelDetailsDialog } from "@/components/parcel-details-dialog"

interface LoadingChart {
  chart_id: number
  chart_number: string
  vehicle_id: number
  destination_branch_id: number
  created_at: string
  status: string
  notes?: string
  loading_type?: string
  total_parcels?: number
  loaded_parcels?: number
  vehicle?: {
    registration_number: string
    vehicle_type: string
  }
  destination_branch?: {
    name: string
    code: string
  }
  memo?: {
    memo_number: string
  }
}

interface LoadingChartItem {
  item_id: number
  lr_number: string
  quantity: number
  status: string
  parcel?: {
    parcel_id: number
    sender_name: string
    recipient_name: string
    sender_phone?: string
    recipient_phone?: string
    current_status: string
    total_items: number
    payment_mode: string
    sender_branch?: {
      name: string
      code: string
    }
    delivery_branch?: {
      name: string
      code: string
    }
  }
}

interface LoadingChartDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  chart: LoadingChart | null
}

const statusColors = {
  "Created": "bg-blue-500",
  "In Transit": "bg-yellow-500",
  "Completed": "bg-green-500",
  "Cancelled": "bg-red-500",
  "Booked": "bg-blue-500",
  "Loaded": "bg-purple-500",
  "Received": "bg-green-500",
  "Delivered": "bg-emerald-500"
}

export function LoadingChartDetailsDialog({ open, onOpenChange, chart }: LoadingChartDetailsDialogProps) {
  const [chartItems, setChartItems] = useState<LoadingChartItem[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedParcel, setSelectedParcel] = useState<any>(null)
  const [isParcelDetailsOpen, setIsParcelDetailsOpen] = useState(false)
  const { toast } = useToast()

  // Fetch chart items when dialog opens
  useEffect(() => {
    if (open && chart) {
      fetchChartItems()
    }
  }, [open, chart])

  const fetchChartItems = async () => {
    if (!chart) return

    setLoading(true)
    try {
      const response = await fetch(`/api/loading-charts/${chart.chart_id}/items`)
      if (!response.ok) {
        throw new Error('Failed to fetch chart items')
      }

      const data = await response.json()
      setChartItems(data.items || [])
    } catch (error: any) {
      console.error('Error fetching chart items:', error)
      toast({
        title: 'Error',
        description: 'Failed to load chart items',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleParcelClick = async (item: LoadingChartItem) => {
    if (!item.parcel?.parcel_id) return

    try {
      const response = await fetch(`/api/parcels/${item.parcel.parcel_id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch parcel details')
      }

      const data = await response.json()

      // Transform the API response to match ParcelDetailsDialog expected format
      const transformedParcel = {
        parcel_id: data.parcel_id,
        lrn: data.lr_number || item.lr_number,
        senderName: data.sender_name || 'N/A',
        senderAddress: data.sender_branch?.address || '',
        senderPhone: data.sender_phone || '',
        senderBranch: data.sender_branch?.name || '',
        recipientName: data.recipient_name || 'N/A',
        recipientAddress: data.delivery_branch?.address || '',
        recipientPhone: data.recipient_phone || '',
        recipientBranch: data.delivery_branch?.name || '',
        status: data.current_status || 'Unknown',
        bookingDate: data.booking_datetime || new Date().toISOString(),
        weight: data.weight?.toString() || '0',
        dimensions: data.dimensions || '',
        price: data.total_amount?.toString() || '0',
        paymentMode: data.payment_mode || 'N/A',
        instructions: data.special_instructions || '',
        numberOfItems: data.number_of_items || 1,
        items: data.items || [],
        statusHistory: data.status_history || []
      }

      setSelectedParcel(transformedParcel)
      setIsParcelDetailsOpen(true)
    } catch (error: any) {
      console.error('Error fetching parcel details:', error)
      toast({
        title: 'Error',
        description: 'Failed to load parcel details',
        variant: 'destructive',
      })
    }
  }

  if (!chart) return null

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Loading Chart Details</span>
            </DialogTitle>
            <DialogDescription>
              View all parcels and details for loading chart {chart.chart_number}
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-[calc(90vh-120px)]">
            <div className="space-y-6">
              {/* Chart Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{chart.chart_number}</span>
                    <Badge 
                      className={`${statusColors[chart.status as keyof typeof statusColors]} text-white`}
                    >
                      {chart.status}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Truck className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Vehicle:</span>
                        <span>
                          {chart.vehicle?.registration_number || 'N/A'}
                          {chart.vehicle?.vehicle_type && ` (${chart.vehicle.vehicle_type})`}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Destination:</span>
                        <span>
                          {chart.destination_branch?.name || 'N/A'}
                          {chart.destination_branch?.code && ` (${chart.destination_branch.code})`}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Created:</span>
                        <span>{format(new Date(chart.created_at), "MMM dd, yyyy HH:mm")}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Parcels:</span>
                        <span>{chart.loaded_parcels || 0} / {chart.total_parcels || 0}</span>
                      </div>
                    </div>
                  </div>

                  {chart.loading_type && (
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Loading Type:</span>
                      <Badge variant="outline">{chart.loading_type}</Badge>
                    </div>
                  )}

                  {chart.memo?.memo_number && (
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Memo:</span>
                      <span>{chart.memo.memo_number}</span>
                    </div>
                  )}

                  {chart.notes && (
                    <div>
                      <span className="font-medium">Notes:</span>
                      <p className="text-sm text-muted-foreground mt-1">{chart.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Chart Items */}
              <Card>
                <CardHeader>
                  <CardTitle>Parcels in this Loading Chart</CardTitle>
                  <CardDescription>
                    Click on any parcel to view detailed information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading parcels...</span>
                    </div>
                  ) : chartItems.length === 0 ? (
                    <div className="text-center py-8">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold">No parcels found</h3>
                      <p className="text-muted-foreground">
                        This loading chart doesn't have any parcels yet.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {chartItems.map((item) => (
                        <Card 
                          key={item.item_id}
                          className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleParcelClick(item)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                  <span className="font-semibold">{item.lr_number}</span>
                                  <Badge 
                                    className={`${statusColors[item.parcel?.current_status as keyof typeof statusColors]} text-white`}
                                  >
                                    {item.parcel?.current_status || 'Unknown'}
                                  </Badge>
                                  <Badge variant="outline">
                                    Qty: {item.quantity}
                                  </Badge>
                                </div>
                                
                                {item.parcel && (
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                    <div className="flex items-center space-x-2">
                                      <User className="h-3 w-3 text-muted-foreground" />
                                      <span>From: {item.parcel.sender_name}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <User className="h-3 w-3 text-muted-foreground" />
                                      <span>To: {item.parcel.recipient_name}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <MapPin className="h-3 w-3 text-muted-foreground" />
                                      <span>
                                        {item.parcel.sender_branch?.name || 'N/A'} → {item.parcel.delivery_branch?.name || 'N/A'}
                                      </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <Package className="h-3 w-3 text-muted-foreground" />
                                      <span>
                                        {item.quantity}/{item.parcel.total_items} items | {item.parcel.payment_mode}
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>
                              
                              <div className="text-right">
                                <Button variant="ghost" size="sm">
                                  View Details
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Parcel Details Dialog */}
      <ParcelDetailsDialog
        open={isParcelDetailsOpen}
        onOpenChange={setIsParcelDetailsOpen}
        parcel={selectedParcel}
      />
    </>
  )
}
