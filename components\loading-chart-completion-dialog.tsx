"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  CheckCircle,
  Download,
  Share2,
  Package,
  Truck,
  MapPin,
  FileText,
  Loader2
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { WhatsAppShareDialog } from "./whatsapp-share-dialog"
import jsPDF from "jspdf"

interface LoadingChartData {
  chart_id: number
  chart_number: string
  vehicle: {
    registration_number: string
    vehicle_type: string
  }
  destination: {
    branch_name: string
    city_name: string
  }
  loading_type: "Direct" | "Via"
  parcels: Array<{
    lr_number: string
    quantity: number
    destination: string
    sender_name?: string
    recipient_name?: string
  }>
  total_parcels: number
  created_at: string
}

interface LoadingChartCompletionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: LoadingChartData
  onClose: () => void
}

export function LoadingChartCompletionDialog({
  open,
  onOpenChange,
  data,
  onClose
}: LoadingChartCompletionDialogProps) {
  const { toast } = useToast()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [showWhatsAppDialog, setShowWhatsAppDialog] = useState(false)

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true)
    const doc = new jsPDF()

    // Add the generated report content (text only)
    doc.setFontSize(12)
    doc.text(generateReportContent(), 10, 10)

    doc.save("receiving-report.pdf") // triggers download with .pdf extension
    try {
      // Placeholder for PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call

      // For now, create a simple text file as placeholder
      const reportContent = generateReportContent()
      const blob = new Blob([reportContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `Loading-Chart-${data.chart_number}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "PDF Downloaded",
        description: "Loading chart report has been downloaded successfully.",
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to generate PDF report. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const generateReportContent = () => {
    return `
LOADING CHART REPORT
====================

Chart Number: ${data.chart_number}
Date: ${new Date(data.created_at).toLocaleDateString()}
Time: ${new Date(data.created_at).toLocaleTimeString()}

VEHICLE INFORMATION
-------------------
Registration: ${data.vehicle.registration_number}
Type: ${data.vehicle.vehicle_type}

DESTINATION
-----------
Branch: ${data.destination.branch_name}
City: ${data.destination.city_name}
Loading Type: ${data.loading_type}

LOADED PARCELS DETAILS
======================
Loading Chart Number | Vehicle Number | LR Numbers | Quantity | Vehicle Type
${'-'.repeat(80)}
${data.parcels.map(parcel => {
  const chartNumber = data.chart_number;
  const vehicleNumber = data.vehicle.registration_number;
  const lrNumber = parcel.lr_number;
  const quantity = parcel.quantity;
  const vehicleType = data.vehicle.vehicle_type;

  return `${chartNumber.padEnd(20)} | ${vehicleNumber.padEnd(14)} | ${lrNumber.padEnd(11)} | ${quantity.toString().padEnd(8)} | ${vehicleType}`;
}).join('\n')}

ADDITIONAL DETAILS
------------------
Total Parcels: ${data.total_parcels}
${data.parcels.map(parcel =>
      `LR: ${parcel.lr_number} | Qty: ${parcel.quantity} | To: ${parcel.destination}`
    ).join('\n')}

Generated by KPN Parcel Service
    `.trim()
  }

  const handleShareWhatsApp = () => {
    setShowWhatsAppDialog(true)
  }

  const handleWhatsAppShare = async (phoneNumbers: string[], message: string) => {
    try {
      // Placeholder for WhatsApp sharing
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Shared Successfully",
        description: `Loading chart report shared with ${phoneNumbers.length} contact(s).`,
      })

      setShowWhatsAppDialog(false)
    } catch (error) {
      toast({
        title: "Share Failed",
        description: "Failed to share report. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <DialogTitle className="text-xl">Loading Chart Created Successfully!</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Chart #{data.chart_number} • {data.total_parcels} parcels loaded
                </p>
              </div>
            </div>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              {/* Vehicle & Destination Info */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Truck className="h-4 w-4" />
                      Vehicle Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Registration</p>
                      <p className="font-medium">{data.vehicle.registration_number}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Type</p>
                      <p className="font-medium">{data.vehicle.vehicle_type}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Destination
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Branch</p>
                      <p className="font-medium">{data.destination.branch_name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">City</p>
                      <p className="font-medium">{data.destination.city_name}</p>
                    </div>
                    <Badge variant={data.loading_type === "Direct" ? "default" : "secondary"}>
                      {data.loading_type} Loading
                    </Badge>
                  </CardContent>
                </Card>
              </div>

              {/* Loaded Parcels */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Loaded Parcels ({data.parcels.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {data.parcels.map((parcel, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium">{parcel.lr_number}</p>
                          <p className="text-sm text-muted-foreground">
                            To: {parcel.destination}
                          </p>
                          {parcel.sender_name && (
                            <p className="text-xs text-muted-foreground">
                              From: {parcel.sender_name}
                            </p>
                          )}
                        </div>
                        <Badge variant="outline">
                          Qty: {parcel.quantity}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
              className="flex-1"
              variant="outline"
            >
              {isGeneratingPDF ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              {isGeneratingPDF ? "Generating..." : "Download PDF Report"}
            </Button>

            <Button
              onClick={handleShareWhatsApp}
              className="flex-1"
              variant="outline"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share via WhatsApp
            </Button>

            <Button onClick={onClose} className="flex-1">
              <FileText className="h-4 w-4 mr-2" />
              Complete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <WhatsAppShareDialog
        open={showWhatsAppDialog}
        onOpenChange={setShowWhatsAppDialog}
        onShare={handleWhatsAppShare}
        reportType="Loading Chart"
        reportNumber={data.chart_number}
      />
    </>
  )
}
