"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTit<PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  CheckCircle, 
  Download, 
  Share2, 
  PackageOpen, 
  Truck, 
  MapPin,
  FileText,
  Loader2,
  AlertTriangle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { WhatsAppShareDialog } from "./whatsapp-share-dialog"
import { ReceivingReport } from "./receiving-report"
import jsPDF from "jspdf"

interface ReceivingCompletionDialogProps {
  isOpen: boolean
  onClose: () => void
  completionData: {
    vehicle: {
      registration_number: string
      vehicle_type: string
      make_model: string
    }
    parcels: Array<{
      lrNumber: string
      quantity: number
      isValid: boolean
      parcelDetails?: {
        sender_name: string
        recipient_name: string
        sender_branch: string
        delivery_branch: string
        // Removed current_status to hide from receiving operators
      }
    }>
    results: Array<{
      lr_number: string
      success: boolean
      received_quantity: number
      // Removed sensitive information: total_received, original_quantity, new_status, warning
      parcel_details?: {
        sender_name: string
        recipient_name: string
        sender_branch: string
        delivery_branch: string
      }
    }>
    summary: {
      total_parcels: number
      successful: number
      failed: number
    }
    receiving_date: string
  }
}

export function ReceivingCompletionDialog({
  isOpen,
  onClose,
  completionData
}: ReceivingCompletionDialogProps) {
  const { toast } = useToast()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [showWhatsAppDialog, setShowWhatsAppDialog] = useState(false)
  const [showReceivingReport, setShowReceivingReport] = useState(false)

  const handleDownloadPDF = () => {
    setShowReceivingReport(true)
    // Show the receiving report component which has PDF/Excel export functionality
    setIsGeneratingPDF(true)
     const doc = new jsPDF()

  // Add the generated report content (text only)
  doc.setFontSize(12)
  doc.text(generateReportContent(), 10, 10)

  doc.save("receiving-report.pdf") // triggers download with .pdf extension
  setIsGeneratingPDF(false)
  }

  // Convert completion data to ReceivingReport format
  const getReceivingReportData = () => {
    return {
      vehicle: {
        registration_number: completionData.vehicle.registration_number,
        vehicle_type: completionData.vehicle.vehicle_type,
        make_model: completionData.vehicle.make_model
      },
      receiving_branch: {
        name: "Current Branch", // This would come from user's branch data
        code: "BR001" // This would come from user's branch data
      },
      receiving_date: completionData.receiving_date,
      operator_name: "Current User", // This would come from user data
      parcels: completionData.results.map(result => ({
        lr_number: result.lr_number,
        sender_name: result.parcel_details?.sender_name || "Unknown",
        recipient_name: result.parcel_details?.recipient_name || "Unknown",
        sender_branch: result.parcel_details?.sender_branch || "Unknown",
        delivery_branch: result.parcel_details?.delivery_branch || "Unknown",
        original_quantity: completionData.parcels.find(p => p.lrNumber === result.lr_number)?.quantity || 0,
        received_quantity: result.received_quantity,
        status: result.success ? "success" : "failed",
        warning: result.success ? undefined : "Failed to receive parcel"
      })),
      summary: {
        total_parcels: completionData.summary.total_parcels,
        total_items_received: completionData.results.reduce((sum, r) => sum + r.received_quantity, 0),
        successful: completionData.summary.successful,
        failed: completionData.summary.failed
      }
    }
  }

  const generateReportContent = () => {
    return `
FLEXIBLE RECEIVING REPORT
=========================

Date: ${new Date(completionData.receiving_date).toLocaleDateString()}
Time: ${new Date(completionData.receiving_date).toLocaleTimeString()}

VEHICLE INFORMATION
-------------------
Registration: ${completionData.vehicle.registration_number}
Type: ${completionData.vehicle.vehicle_type}
Model: ${completionData.vehicle.make_model}

RECEIVING SUMMARY
-----------------
Total Parcels: ${completionData.summary.total_parcels}
Successful: ${completionData.summary.successful}
Failed: ${completionData.summary.failed}

RECEIVED PARCELS DETAILS
========================
Vehicle Number | LR Numbers | Quantity | Vehicle Type
${'-'.repeat(60)}
${completionData.results.map(result => {
  const vehicleNumber = completionData.vehicle.registration_number;
  const lrNumber = result.lr_number;
  const quantity = result.received_quantity;
  const vehicleType = completionData.vehicle.vehicle_type;

  return `${vehicleNumber.padEnd(14)} | ${lrNumber.padEnd(10)} | ${quantity.toString().padEnd(8)} | ${vehicleType}`;
}).join('\n')}

ADDITIONAL DETAILS
------------------
${completionData.results.map(result =>
  `LR: ${result.lr_number} | Status: ${result.success ? 'Success' : 'Failed'} | Received: ${result.received_quantity}`
).join('\n')}

Generated by KPN Parcel Service - Flexible Receiving System
    `.trim()
  }

  const handleShareWhatsApp = () => {
    setShowWhatsAppDialog(true)
  }

  const handleWhatsAppShare = async (phoneNumbers: string[], message: string) => {
    try {
      // Placeholder for WhatsApp sharing
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Shared Successfully",
        description: `Receiving report shared with ${phoneNumbers.length} contact(s).`,
      })
      
      setShowWhatsAppDialog(false)
    } catch (error) {
      toast({
        title: "Share Failed",
        description: "Failed to share report. Please try again.",
        variant: "destructive",
      })
    }
  }

  const hasWarnings = false // No warnings in current interface
  const hasFailures = completionData.summary.failed > 0

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <DialogTitle className="text-xl">Receiving Operation Completed!</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {completionData.summary.successful} of {completionData.summary.total_parcels} parcels received successfully
                </p>
              </div>
            </div>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              {/* Warnings and Failures */}
              {(hasWarnings || hasFailures) && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 text-orange-800">
                      <AlertTriangle className="h-4 w-4" />
                      <div>
                        {hasFailures && (
                          <p className="text-sm font-medium">
                            {completionData.summary.failed} parcel(s) failed to process.
                          </p>
                        )}
                        {hasWarnings && (
                          <p className="text-sm font-medium">
                            Some parcels have warnings that require attention.
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Vehicle Information */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    Vehicle Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Registration</p>
                      <p className="font-medium">{completionData.vehicle.registration_number}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Type</p>
                      <p className="font-medium">{completionData.vehicle.vehicle_type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Model</p>
                      <p className="font-medium">{completionData.vehicle.make_model}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Received Parcels */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <PackageOpen className="h-4 w-4" />
                    Processing Results ({completionData.results.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {completionData.results.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{result.lr_number}</p>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? "Success" : "Failed"}
                            </Badge>
                          </div>
                          {result.parcel_details && (
                            <div className="text-sm text-muted-foreground mt-1">
                              <p>{result.parcel_details.sender_name} → {result.parcel_details.recipient_name}</p>
                              <p>{result.parcel_details.sender_branch} → {result.parcel_details.delivery_branch}</p>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm">
                            <span className="text-muted-foreground">Received:</span> {result.received_quantity}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
              className="flex-1"
              variant="outline"
            >
              {isGeneratingPDF ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              {isGeneratingPDF ? "Generating..." : "Download PDF Report"}
            </Button>
            
            <Button
              onClick={handleShareWhatsApp}
              className="flex-1"
              variant="outline"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share via WhatsApp
            </Button>
            
            <Button onClick={onClose} className="flex-1">
              <FileText className="h-4 w-4 mr-2" />
              Complete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <WhatsAppShareDialog
        open={showWhatsAppDialog}
        onOpenChange={setShowWhatsAppDialog}
        onShare={handleWhatsAppShare}
        reportType="Flexible Receiving Report"
        reportNumber={`${new Date(completionData.receiving_date).toISOString().split('T')[0]}`}
      />

      {/* Receiving Report Dialog */}
      {showReceivingReport && (
        <Dialog open={showReceivingReport} onOpenChange={setShowReceivingReport}>
          <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden p-0">
            <div className="p-6 overflow-y-auto max-h-[95vh]">
              <ReceivingReport
                reportData={getReceivingReportData()}
                onClose={() => setShowReceivingReport(false)}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
