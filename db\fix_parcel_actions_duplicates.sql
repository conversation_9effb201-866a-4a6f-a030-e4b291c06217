-- Fix Parcel Actions Duplicate Issues
-- This script addresses the duplicate action creation problem and missing "Booked" actions

-- Step 1: Remove duplicate-creating triggers
-- These triggers create generic "Status updated to [Status]" actions that duplicate the detailed actions
-- created by the application code

DROP TRIGGER IF EXISTS parcel_status_change_trigger ON parcels;
DROP TRIGGER IF EXISTS record_parcel_status_change_trigger ON parcels;

-- Step 2: Remove the functions that create duplicate actions
DROP FUNCTION IF EXISTS create_parcel_action_on_status_change();
DROP FUNCTION IF EXISTS record_parcel_status_change();

-- Step 3: Create a new trigger function that only creates "Booked" actions on INSERT
-- This ensures every parcel gets an initial "Booked" action when created
CREATE OR REPLACE FUNCTION create_initial_parcel_action()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_name VARCHAR(255);
BEGIN
  -- Only create action for new parcels (INSERT operation)
  -- Get sender branch name for the booking location
  SELECT name INTO v_branch_name 
  FROM branches 
  WHERE branch_id = NEW.sender_branch_id;
  
  -- Insert initial "Booked" action record
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    remarks,
    created_by
  ) VALUES (
    NEW.parcel_id,
    'Booked',
    NEW.booking_datetime,
    NEW.sender_branch_id,
    v_branch_name,
    'Parcel booked at origin branch',
    NULL -- Will be set by application code when available
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger for initial parcel booking action
CREATE TRIGGER create_initial_parcel_action_trigger
  AFTER INSERT ON parcels
  FOR EACH ROW
  EXECUTE FUNCTION create_initial_parcel_action();

-- Step 5: Clean up existing duplicate actions
-- Remove generic "Status updated to [Status]" actions that have detailed counterparts

-- First, identify and remove duplicate "Loaded" actions
DELETE FROM parcel_actions 
WHERE action_id IN (
  SELECT pa1.action_id
  FROM parcel_actions pa1
  WHERE pa1.action_type = 'Loaded'
    AND pa1.remarks = 'Status updated to Loaded'
    AND pa1.vehicle_id IS NULL
    AND pa1.quantity_loaded IS NULL
    AND EXISTS (
      SELECT 1 
      FROM parcel_actions pa2 
      WHERE pa2.parcel_id = pa1.parcel_id
        AND pa2.action_type = 'Loaded'
        AND pa2.action_id != pa1.action_id
        AND pa2.vehicle_id IS NOT NULL
        AND pa2.quantity_loaded IS NOT NULL
        AND ABS(EXTRACT(EPOCH FROM (pa2.action_timestamp - pa1.action_timestamp))) < 10
    )
);

-- Remove duplicate "Received" actions
DELETE FROM parcel_actions 
WHERE action_id IN (
  SELECT pa1.action_id
  FROM parcel_actions pa1
  WHERE pa1.action_type = 'Received'
    AND pa1.remarks = 'Status updated to Received'
    AND pa1.vehicle_id IS NULL
    AND pa1.quantity_received IS NULL
    AND EXISTS (
      SELECT 1 
      FROM parcel_actions pa2 
      WHERE pa2.parcel_id = pa1.parcel_id
        AND pa2.action_type = 'Received'
        AND pa2.action_id != pa1.action_id
        AND pa2.vehicle_id IS NOT NULL
        AND pa2.quantity_received IS NOT NULL
        AND ABS(EXTRACT(EPOCH FROM (pa2.action_timestamp - pa1.action_timestamp))) < 10
    )
);

-- Step 6: Add missing "Booked" actions for existing parcels
-- Insert "Booked" actions for parcels that don't have them
INSERT INTO parcel_actions (
  parcel_id,
  action_type,
  action_timestamp,
  branch_id,
  location_name,
  remarks
)
SELECT 
  p.parcel_id,
  'Booked',
  p.booking_datetime,
  p.sender_branch_id,
  b.name,
  'Parcel booked at origin branch'
FROM parcels p
JOIN branches b ON b.branch_id = p.sender_branch_id
WHERE NOT EXISTS (
  SELECT 1 
  FROM parcel_actions pa 
  WHERE pa.parcel_id = p.parcel_id 
    AND pa.action_type = 'Booked'
);

-- Step 7: Add comments for documentation
COMMENT ON FUNCTION create_initial_parcel_action() IS 'Creates initial "Booked" action when a parcel is first created';
COMMENT ON TRIGGER create_initial_parcel_action_trigger ON parcels IS 'Automatically creates "Booked" action for new parcels';

-- Step 8: Verify the cleanup
-- Show summary of actions by type after cleanup
SELECT 
  action_type,
  COUNT(*) as action_count,
  COUNT(DISTINCT parcel_id) as unique_parcels
FROM parcel_actions 
GROUP BY action_type 
ORDER BY action_type;

-- Show any remaining potential duplicates
SELECT 
  parcel_id,
  action_type,
  COUNT(*) as duplicate_count
FROM parcel_actions 
GROUP BY parcel_id, action_type 
HAVING COUNT(*) > 1
ORDER BY parcel_id, action_type;
