import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/auth/validate-email-for-reset
// This endpoint is publicly accessible for password reset validation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { valid: false, message: "Email is required" },
        { status: 400 },
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { valid: false, message: "Please enter a valid email address" },
        { status: 400 },
      );
    }

    // Normalize the email for consistent comparison
    const normalizedEmail = email.trim().toLowerCase();

    // Check if the email exists in the users table
    // Use ilike for case-insensitive matching
    const { data: user, error } = await supabase
      .from("users")
      .select("user_id, email, branch_id, role")
      .ilike("email", normalizedEmail)
      .single();

    if (error || !user) {
      // Don't reveal whether the email exists or not for security
      // Always return the same message to prevent email enumeration
      return NextResponse.json(
        {
          valid: false,
          message:
            "If this email is registered in our system, you will receive a password reset link.",
        },
        { status: 200 },
      );
    }

    // Additional security: Check if user account is active (if such field exists)
    // For now, we'll assume all users in the table are valid for password reset

    // Email exists in the system and user is valid
    return NextResponse.json(
      {
        valid: true,
        message: "Email validation successful",
      },
      { status: 200 },
    );
  } catch (error: any) {
    console.error("Error validating email for reset:", error);
    return NextResponse.json(
      {
        valid: false,
        message: "An error occurred while validating the email",
      },
      { status: 500 },
    );
  }
}
